# Python 3.12.8 compatible requirements
# Core web framework
fastapi>=0.104.0,<0.105.0
uvicorn[standard]>=0.24.0,<0.25.0
python-multipart>=0.0.6,<0.1.0

# Database
psycopg2-binary>=2.9.7,<3.0.0
pgvector>=0.2.3,<0.3.0
sqlalchemy>=2.0.20,<3.0.0
alembic>=1.12.0,<2.0.0

# Document processing
pymupdf>=1.23.0,<2.0.0
python-docx>=1.1.0,<2.0.0
openpyxl>=3.1.0,<4.0.0
python-pptx>=0.6.20,<1.0.0

# AI and ML
langchain>=0.1.0,<0.2.0
langchain-google-genai>=0.0.5,<0.1.0
google-generativeai>=0.3.0,<0.4.0
sentence-transformers>=2.2.0,<3.0.0
numpy>=1.24.0,<2.0.0

# Utilities
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.5.0,<3.0.0
aiofiles>=23.2.0,<24.0.0
# psutil>=5.9.0,<6.0.0



streamlit>=1.28.0,<1.29.0

# HTTP client
requests>=2.31.0,<3.0.0

# Data manipulation and analysis
pandas>=2.0.0,<3.0.0
# Interactive charts and visualization
plotly>=5.17.0,<6.0.0

# Streamlit extensions
streamlit-option-menu>=0.3.6,<0.4.0
streamlit-file-browser



# Additional dependencies for Python 3.12 compatibility
setuptools>=68.0.0
wheel>=0.41.0 
psutil>=7.0.0