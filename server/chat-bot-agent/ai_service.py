import google.generativeai as genai
from typing import List, Dict, Any, Optional
import logging
import json
from config import config
from schemas import Chunk, ChatMessageResponse

logger = logging.getLogger(__name__)


class AIService:
    """Handles AI interactions using Google Gemini"""

    def __init__(self):
        """Initialize the AI service with Google Gemini"""
        if not config.GOOGLE_API_KEY:
            raise ValueError("GOOGLE_API_KEY is required")

        genai.configure(api_key=config.GOOGLE_API_KEY)

        # Try different model names to find the correct one
        try:
            self.model = genai.GenerativeModel("gemini-1.5-pro")
            logger.info("Using gemini-1.5-pro model")
        except Exception as e1:
            try:
                self.model = genai.GenerativeModel("gemini-1.5-flash")
                logger.info("Using gemini-1.5-flash model")
            except Exception as e2:
                try:
                    self.model = genai.GenerativeModel("gemini-pro")
                    logger.info("Using gemini-pro model")
                except Exception as e3:
                    # Fallback to the most basic model
                    self.model = genai.GenerativeModel("gemini-1.0-pro")
                    logger.info("Using gemini-1.0-pro model")

        # System prompt for LE compliance chatbot
        self.system_prompt = """You are Lexi, a Legal and Ethical Compliance Assistant. Your role is to:

1. Detect the language of the user's message and respond in the same language
2. Respond naturally and contextually to user messages
3. Analyze legal and compliance documents provided in the context
4. Answer questions based ONLY on the information found in the uploaded documents
5. Provide accurate, factual responses without speculation
6. If information is not available in the documents, clearly state that
7. Maintain professional and objective tone
8. Cite specific sections or parts of documents when possible

RESPONSE BEHAVIOR:
- If the user asks a direct question about documents or compliance, answer immediately without introducing yourself
- If the user asks about your name, role, or capabilities, provide a brief, natural response
- If the user greets you (hello, hi, etc.), respond warmly and briefly introduce yourself as "Lexi, your Legal and Ethical Compliance Assistant"
- Focus on being helpful and direct rather than repetitive
- Be conversational and human-like in your responses

LANGUAGE DETECTION: Always detect the language of the user's input and respond in the same language. If the user writes in Spanish, respond in Spanish. If they write in French, respond in French. If they write in German, respond in German. And so on for any language they use.

IMPORTANT: Only use information from the provided document context. Do not use external knowledge unless specifically relevant to interpreting the document content.

RESPONSE FORMAT: You must respond in the following JSON format:
{
    "response": "Your generated response text here",
    "citations": {
        "document_used_chunks": [list of chunk IDs you used from the DOCUMENT CONTEXT]
    }
}

The chunk IDs should be the numbers from the "Chunk ID: X" labels in the document context."""

        # Enhanced system prompt for context awareness
        self.context_aware_prompt = f"""{self.system_prompt}

CONVERSATION AWARENESS:
- You have access to previous conversation context via embeddings
- Reference relevant previous exchanges when appropriate
- Maintain conversation continuity
- Respond naturally without unnecessary repetition
- Avoid mentioning that the user has asked similar questions before unless it's directly relevant
- Focus on providing the requested information directly and naturally

CONTEXT SOURCES:
- Document chunks: Information from uploaded documents
- Conversation context: Relevant previous messages (found via embeddings)
- Chat history: Recent conversation flow
- Available documents: List of documents that are available to reference

SELF-AWARENESS FEATURES:
- Acknowledge when you're making assumptions vs. using document facts
- Explain reasoning process when specifically asked
- Identify when information is missing from documents
- Distinguish between document-based answers and general knowledge
- Explain how you arrived at your conclusions when specifically asked

Please use all available context to provide comprehensive, contextually aware responses that are direct and helpful."""



    def generate_response_with_context(
        self,
        query: str,
        document_chunks: List[Chunk],
        conversation_context: List[Dict[str, Any]],
        chat_history: List[ChatMessageResponse] = [],
        documents_list: List[Dict[str, Any]] = [],

    ) -> Dict[str, Any]:
        """
        Generate response with full context awareness

        Args:
            query: User's question
            document_chunks: List of relevant document chunks
            conversation_context: List of relevant conversation context
            chat_history: Recent chat history

        Returns:
            Dictionary with response and metadata
        """
        try:
            # Build comprehensive prompt
            full_prompt = self._build_context_aware_prompt(
                query, document_chunks, conversation_context, chat_history, documents_list
            )

            print("-------------------------------- Full Prompt --------------------------------")
            print(full_prompt)
            print("-------------------------------- End Full Prompt --------------------------------")  

            # Generate response
            print("-------------------------------- Generating AI Response --------------------------------")
            response = self.model.generate_content(full_prompt)
            print(f"Raw AI Response: {response.text}")
            print("-------------------------------- End Raw Response --------------------------------")
            
            # Clean the response text to remove markdown code blocks
            cleaned_response = response.text.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]  # Remove ```json
            if cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[3:]  # Remove ```
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]  # Remove trailing ```
            cleaned_response = cleaned_response.strip()
            
            print(f"Cleaned Response: {cleaned_response}")
            
            # Try to parse as JSON
            try:
                print("-------------------------------- Attempting JSON Parse --------------------------------")
                parsed_response_data = json.loads(cleaned_response)

                print("-------------------------------- Parsed Response --------------------------------")
                print(parsed_response_data)
                print("-------------------------------- End Parsed Response --------------------------------")

                # Normalize if the model returned a list instead of an object
                if isinstance(parsed_response_data, list):
                    if parsed_response_data and isinstance(parsed_response_data[0], dict):
                        parsed_response_data = parsed_response_data[0]
                    else:
                        # Fall back to returning the cleaned response as plain text
                        return {
                            "response": cleaned_response,
                            "citations": {"document_used_chunks": []},
                            "confidence": self._assess_confidence(
                                document_chunks, conversation_context
                            ),
                            "reasoning": self._extract_reasoning(
                                cleaned_response, document_chunks, conversation_context
                            ),
                        }

                # Normalize citations if they came back as a simple list
                citations_value = parsed_response_data.get("citations", {"document_used_chunks": []})
                if isinstance(citations_value, list):
                    normalized = {"document_used_chunks": []}
                    for item in citations_value:
                        if isinstance(item, int):
                            normalized["document_used_chunks"].append(item)
                        elif isinstance(item, str) and item.isdigit():
                            normalized["document_used_chunks"].append(int(item))
                    citations_value = normalized

                return {
                    "response": parsed_response_data.get("response", "Could not parse response"),
                    "citations": citations_value,
                    "confidence": self._assess_confidence(
                        document_chunks, conversation_context
                    ),
                    "reasoning": self._extract_reasoning(
                        parsed_response_data.get("response", response.text), document_chunks, conversation_context
                    ),
                }
            except json.JSONDecodeError as e:
                print(f"-------------------------------- JSON Parse Error --------------------------------")
                print(f"Error: {e}")
                print(f"Cleaned response text: {cleaned_response}")
                print("-------------------------------- End JSON Parse Error --------------------------------")
                # If JSON parsing fails, return formatted response
                return {
                    "response": cleaned_response,
                    "citations": {"document_used_chunks": []},
                    "confidence": self._assess_confidence(
                        document_chunks, conversation_context
                    ),
                    "reasoning": self._extract_reasoning(
                        cleaned_response, document_chunks, conversation_context
                    ),
                }

        except Exception as e:
            print(f"-------------------------------- Main Exception --------------------------------")
            print(f"Error: {e}")
            print("-------------------------------- End Main Exception --------------------------------")
            logger.error(f"Error generating context-aware response: {e}")
            return {
                "response": f"I apologize, but I encountered an error: {str(e)}",
                "citations": {"document_used_chunks": []},
                "confidence": "error",
                "reasoning": "Error occurred during response generation",
            }

    def _build_context_aware_prompt(
        self,
        query: str,
        document_chunks: List[Chunk],
        conversation_context: List[Dict[str, Any]],
        chat_history: List[ChatMessageResponse],
        documents_list: List[Dict[str, Any]],
    ) -> str:
        """
        Build comprehensive prompt with all context sources
        """
        # Format conversation context
        conv_context_text = ""
        if conversation_context:
            conv_context_text = "RELEVANT CONVERSATION CONTEXT:\n"
            for ctx in conversation_context:
                role = "User" if ctx["messageType"] == "user" else "Assistant"
                conv_context_text += f"{role}: {ctx['content']}\n"

        # Format documents list
        documents_list_text = ""
        if documents_list:
            documents_list_text = "AVAILABLE DOCUMENTS:\n"
            for i, doc in enumerate(documents_list, 1):
                documents_list_text += f"{i}. {doc.get('name', 'Unknown Document')}\n"
            documents_list_text += "\n"

        # Format document context
        doc_context_text = self._prepare_context(document_chunks)

        # Format recent chat history
        chat_history_text = ""
        if chat_history:
            chat_history_text = "RECENT CONVERSATION HISTORY:\n"
            for msg in chat_history[-5:]:  # Last 5 messages
                role = "User" if msg.type == "user" else "Assistant"
                chat_history_text += f"{role}: {msg.content}\n"

        return f"""{self.context_aware_prompt}

{documents_list_text}

DOCUMENT CONTEXT:
{doc_context_text}

{chat_history_text}


CURRENT QUERY: {query}

Please provide a comprehensive response that:
1. Answers the current question directly and naturally
2. References relevant previous conversation when appropriate, but avoid mentioning repetitive questions
3. Uses document information when available
4. Maintains conversation continuity
5. Explains your reasoning process when specifically asked
6. If asked about documents being referenced, relevant, or available, provide a list in ordered format. Use the available documents list to reference the documents
7. Focus on being helpful and direct rather than pointing out repetition

RESPONSE FORMAT: You must respond in the following JSON format:
{{
    "response": "Your generated response text here",
    "citations": {{
        "document_used_chunks": [list of chunk IDs you used from the DOCUMENT CONTEXT]
    }}
}}

The chunk IDs should be the numbers from the "Chunk ID: X" labels in the document context."""

    def _prepare_context(self, chunks: List[Chunk]) -> str:
        """
        Prepare context text from document chunks

        Args:
            chunks: List of Chunk objects with metadata

        Returns:
            Formatted context string
        """
        if not chunks:
            return "No relevant document context found."

        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            chunk_text = chunk.chunk_text
            document_name = chunk.document_name
            chunk_index = chunk.chunk_index
            chunk_id = chunk.chunk_id

            context_parts.append(f"Chunk ID: {chunk_id}")
            context_parts.append(f"Document: {document_name}")
            context_parts.append(f"Section {chunk_index}:")
            context_parts.append(chunk_text)
            context_parts.append("---")

        return "\n".join(context_parts)

    def _assess_confidence(
        self,
        document_chunks: List[Chunk],
        conversation_context: List[Dict[str, Any]],
    ) -> str:
        """
        Assess confidence based on available context
        """
        if document_chunks and conversation_context:
            return "high"
        elif document_chunks:
            return "medium"
        elif conversation_context:
            return "medium"
        else:
            return "low"

    def _extract_reasoning(
        self,
        response_text: str,
        document_chunks: List[Chunk],
        conversation_context: List[Dict[str, Any]],
    ) -> str:
        """
        Extract reasoning from response and context
        """
        reasoning_parts = []

        if document_chunks:
            reasoning_parts.append(
                f"Based on {len(document_chunks)} relevant document sections"
            )

        if conversation_context:
            reasoning_parts.append(
                f"Referenced {len(conversation_context)} relevant previous exchanges"
            )

        if reasoning_parts:
            return " | ".join(reasoning_parts)
        else:
            return (
                "Response generated based on general knowledge and conversation context"
            )


