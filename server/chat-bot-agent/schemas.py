from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import UUID


# NEW SCHEMA MODELS FOR CHUNKS AND DOCUMENT CONTEXT
class Chunk(BaseModel):
    """Schema for document chunk with metadata"""
    chunk_id: int = Field(..., description="Unique chunk identifier")
    chunk_text: str = Field(..., description="Text content of the chunk")
    chunk_index: int = Field(..., description="Index of the chunk within the document")
    document_id: int = Field(..., description="ID of the parent document")
    document_name: str = Field(..., description="Name of the parent document")
    page_number: Optional[int] = Field(None, description="Page number where chunk appears")
    section: Optional[str] = Field(None, description="Section name where chunk appears")


class DocumentCitation(BaseModel):
    """Schema for document citation with chunks"""
    document_id: int = Field(..., description="ID of the cited document")
    document_name: str = Field(..., description="Name of the cited document")
    chunks: List[Chunk] = Field(..., description="List of chunks from this document")


class DocumentContext(BaseModel):
    """Schema for document context with citations"""
    citations: List[DocumentCitation] = Field(..., description="List of document citations")
    totalDocuments: int = Field(..., description="Total number of documents cited")
    totalChunks: int = Field(..., description="Total number of chunks used")


class DocumentUploadRequest(BaseModel):
    userId: UUID = Field(..., description="User ID (required)")
    companyId: Optional[UUID] = Field(None, description="Company ID (optional)")
    documentType: Optional[str] = Field(
        "Standard", description="Document type (Standard or Compliance)"
    )


class DocumentUploadResponse(BaseModel):
    id: int
    filename: str
    originalFilename: str
    fileSize: int
    fileType: str
    uploadedAt: datetime
    totalPages: Optional[int] = None
    totalChunks: Optional[int] = None
    chunksCount: Optional[int] = None
    documentType: Optional[str] = "Standard"
    isVectorEmbeddingComplete: bool = False


class MultipleDocumentUploadRequest(BaseModel):
    userId: UUID = Field(..., description="User ID (required)")
    companyId: Optional[UUID] = Field(None, description="Company ID (optional)")


class DocumentUploadResult(BaseModel):
    documentId: int
    taskId: str
    status: str = "queued"
    message: str = "Document uploaded and queued for processing"
    id: int
    filename: str
    originalFilename: str
    fileSize: int
    fileType: str
    uploadedAt: datetime
    totalPages: Optional[int] = None
    totalChunks: Optional[int] = None
    chunksCount: Optional[int] = None
    documentType: Optional[str] = "Standard"


class MultipleDocumentUploadResponse(BaseModel):
    uploads: List[DocumentUploadResult]
    totalCount: int
    message: str = "Documents uploaded and queued for processing"


class ChatQueryRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000)
    userId: UUID = Field(..., description="User ID (required)")
    companyId: Optional[UUID] = Field(None, description="Company ID (optional)")
    sessionId: str = Field(..., description="Session ID (required)")





# NEW ENHANCED SCHEMAS FOR CONVERSATION AWARENESS
class ConversationContextItem(BaseModel):
    id: int
    content: str
    messageType: str
    timestamp: str
    queryType: Optional[str] = None
    confidence: Optional[str] = None


class EnhancedChatQueryResponse(BaseModel):
    id: int
    sessionId: str
    messageType: str
    content: str
    timestamp: str
    queryType: Optional[str] = None
    confidence: Optional[str] = None
    reasoning: Optional[str] = None
    documentContext: Optional[DocumentContext] = None  # New field for document citations (nullable)


class ChatMessageResponse(BaseModel):
    id: int
    type: str
    content: str
    timestamp: str
    documentContext: Optional[DocumentContext] = None  # New field for document citations (nullable)
    queryType: Optional[str] = None
    confidence: Optional[str] = None
    reasoning: Optional[str] = None


class ChatHistoryResponse(BaseModel):
    sessionId: str
    messages: List[ChatMessageResponse]


class SessionInfoResponse(BaseModel):
    sessionId: str
    userId: str
    companyId: Optional[str] = None
    createdAt: str
    messageCount: int
    name: Optional[str] = None


class SessionListItem(BaseModel):
    sessionId: str
    userId: str
    companyId: Optional[str] = None
    name: str
    createdAt: str
    lastActivity: str
    messageCount: int
    preview: str


class SessionListResponse(BaseModel):
    sessions: List[SessionListItem]
    totalCount: int


class DocumentInfo(BaseModel):
    id: int
    filename: str
    originalFilename: str
    fileSize: int
    fileType: str
    uploadedAt: datetime
    totalPages: Optional[int] = None
    totalChunks: Optional[int] = None
    chunksCount: Optional[int] = None
    documentType: Optional[str] = "Standard"  # Standard or Compliance
    isVectorEmbeddingComplete: bool = False


class DocumentListResponse(BaseModel):
    documents: List[DocumentInfo]
    totalCount: int


class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None


# Queue system schemas


class DocumentUploadQueuedResponse(BaseModel):
    documentId: int
    taskId: str
    filename: str
    fileSize: int
    status: str = "queued"
    message: str = "Document uploaded and queued for processing"


class TaskStatusResponse(BaseModel):
    taskId: str
    status: str  # queued, processing, completed, failed
    progress: int  # 0-100
    errorMessage: Optional[str] = None
    chunksCreated: int
    createdAt: float
    startedAt: Optional[float] = None
    completedAt: Optional[float] = None
    originalFilename: str
    # Position in queue (1-based, None if not queued)
    queuePosition: Optional[int] = None


class QueueStatusResponse(BaseModel):
    queueSize: int
    queuedTasks: int
    processingTasks: int
    completedTasks: int
    failedTasks: int
    totalTasks: int
