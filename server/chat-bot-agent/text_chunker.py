import re
from typing import List, Dict, Any, Optional
from config import config

class TextChunker:
    """Handles text chunking for document processing"""
    
    def __init__(self, chunk_size: Optional[int] = None, chunk_overlap: Optional[int] = None):
        self.chunk_size = chunk_size or config.CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or config.CHUNK_OVERLAP
    
    def sanitize_text(self, text: str) -> str:
        """Remove NUL characters and other problematic characters from text"""
        if not text:
            return ""
        
        # Remove NUL characters (0x00) - these cause PostgreSQL errors
        text = text.replace('\x00', '')
        
        # Remove other control characters that might cause issues
        # Keep newlines, tabs, and carriage returns but remove others
        text = ''.join(char for char in text if char >= ' ' or char in '\n\t\r')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def split_text_by_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Sanitize text first
        text = self.sanitize_text(text)
        
        # Split by sentence endings, preserving the endings
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def split_text_by_paragraphs(self, text: str) -> List[str]:
        """Split text into paragraphs"""
        # Sanitize text first
        text = self.sanitize_text(text)
        
        paragraphs = text.split('\n\n')
        return [p.strip() for p in paragraphs if p.strip()]
    
    def create_chunks(self, pages_data: List[Dict[str, Any]], document_id: int) -> List[Dict[str, Any]]:
        """Create chunks from pages data with metadata"""
        chunks = []
        chunk_index = 0
        
        for page_data in pages_data:
            page_number = page_data.get('page_number', 1)
            section = page_data.get('section', 'Unknown')
            text = page_data.get('text', '')
            
            # Sanitize the text before processing
            text = self.sanitize_text(text)
            
            if not text.strip():
                continue
            
            # Create chunks for this page
            page_chunks = self._create_chunks_from_text(
                text, document_id, chunk_index, page_number, section
            )
            chunks.extend(page_chunks)
            chunk_index += len(page_chunks)
        
        return chunks
    
    def _create_chunks_from_text(self, text: str, document_id: int, start_chunk_index: int, 
                                page_number: int, section: str) -> List[Dict[str, Any]]:
        """Create chunks from text with specific page and section metadata"""
        chunks = []
        
        # First try to split by paragraphs
        paragraphs = self.split_text_by_paragraphs(text)
        
        current_chunk = ""
        chunk_index = start_chunk_index
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                chunks.append({
                    'document_id': document_id,
                    'chunk_text': current_chunk.strip(),
                    'chunk_index': chunk_index,
                    'page_number': page_number,
                    'section': section
                })
                chunk_index += 1
                
                # Start new chunk with overlap
                if self.chunk_overlap > 0:
                    # Take last few sentences for overlap
                    sentences = self.split_text_by_sentences(current_chunk)
                    overlap_text = " ".join(sentences[-2:]) if len(sentences) >= 2 else sentences[-1] if sentences else ""
                    current_chunk = overlap_text + " " + paragraph
                else:
                    current_chunk = paragraph
            else:
                current_chunk += " " + paragraph if current_chunk else paragraph
        
        # Add the last chunk if it exists
        if current_chunk.strip():
            chunks.append({
                'document_id': document_id,
                'chunk_text': current_chunk.strip(),
                'chunk_index': chunk_index,
                'page_number': page_number,
                'section': section
            })
        
        # If chunks are still too large, split by sentences
        final_chunks = []
        for chunk in chunks:
            if len(chunk['chunk_text']) > self.chunk_size:
                sentence_chunks = self._split_chunk_by_sentences(chunk)
                final_chunks.extend(sentence_chunks)
            else:
                final_chunks.append(chunk)
        
        return final_chunks
    
    def _split_chunk_by_sentences(self, chunk: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split a chunk by sentences if it's still too large"""
        sentences = self.split_text_by_sentences(chunk['chunk_text'])
        sentence_chunks = []
        current_chunk = ""
        chunk_index = chunk['chunk_index']
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                sentence_chunks.append({
                    'document_id': chunk['document_id'],
                    'chunk_text': current_chunk.strip(),
                    'chunk_index': chunk_index,
                    'page_number': chunk['page_number'],
                    'section': chunk['section']
                })
                chunk_index += 1
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        if current_chunk.strip():
            sentence_chunks.append({
                'document_id': chunk['document_id'],
                'chunk_text': current_chunk.strip(),
                'chunk_index': chunk_index,
                'page_number': chunk['page_number'],
                'section': chunk['section']
            })
        
        return sentence_chunks
    
    # Legacy method for backward compatibility
    def create_chunks_legacy(self, text: str, document_id: int) -> List[Dict[str, Any]]:
        """Legacy method that creates chunks from plain text (for backward compatibility)"""
        # Sanitize text first
        text = self.sanitize_text(text)
        
        # Convert plain text to pages format for consistency
        pages_data = [{
            'page_number': 1,
            'text': text,
            'section': 'Main Content',
            'header': '',
            'footer': ''
        }]
        return self.create_chunks(pages_data, document_id) 