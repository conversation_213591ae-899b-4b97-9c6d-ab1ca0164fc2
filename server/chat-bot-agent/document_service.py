import os
import uuid
from typing import List, Dict, Any, <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import text, cast, String
import logging
from uuid import UUID

from models import Document, DocumentChunk
from document_processor import DocumentProcessor
from text_chunker import <PERSON><PERSON>hun<PERSON>
from embedding_service import EmbeddingService
from config import config
from schemas import Chunk

logger = logging.getLogger(__name__)


class DocumentService:
    """Orchestrates document processing pipeline"""

    def __init__(self):
        self.document_processor = DocumentProcessor(config.UPLOAD_DIR)
        self.text_chunker = TextChunker()
        self.embedding_service = EmbeddingService()

    def create_document_record(
        self,
        file_content: bytes,
        original_filename: str,
        db: Session,
        user_id: UUID,
        company_id: Optional[UUID] = None,
        document_type: Optional[str] = "Standard",
    ) -> Dict[str, Any]:
        """
        Create a document record without processing (for queue system)

        Args:
            file_content: Raw file content
            original_filename: Original filename
            db: Database session
            user_id: User ID (required)
            company_id: Company ID (optional)
            document_type: Document type (Standard or Compliance, defaults to Standard)

        Returns:
            Dictionary with document record info
        """
        try:
            # Validate file type
            file_extension = os.path.splitext(original_filename)[1].lower()
            if file_extension not in config.SUPPORTED_EXTENSIONS:
                raise ValueError(f"Unsupported file type: {file_extension}")

            # Generate unique filename
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # Create document record (without saving file or processing)
            document = Document(
                filename=unique_filename,
                originalFilename=original_filename,
                filePath="",  # Will be set during processing
                fileSize=len(file_content),
                fileType=file_extension,
                documentType=document_type,
                userId=user_id,
                companyId=company_id,
            )

            db.add(document)
            db.commit()
            db.refresh(document)

            return {
                "documentId": document.id,
                "filename": original_filename,
                "fileSize": len(file_content),
                "status": "created",
            }

        except Exception as e:
            logger.error(f"Error creating document record {original_filename}: {e}")
            db.rollback()
            raise

    def process_document(
        self,
        file_content: bytes,
        original_filename: str,
        db: Session,
        user_id: UUID,
        company_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Process uploaded document through the complete pipeline

        Args:
            file_content: Raw file content
            original_filename: Original filename
            db: Database session
            user_id: User ID (required)
            company_id: Company ID (optional)

        Returns:
            Dictionary with processing results
        """
        try:
            # Validate file type
            file_extension = os.path.splitext(original_filename)[1].lower()
            if file_extension not in config.SUPPORTED_EXTENSIONS:
                raise ValueError(f"Unsupported file type: {file_extension}")

            # Generate unique filename
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # Save file
            file_path = self.document_processor.save_file(file_content, unique_filename)

            # Extract text with page and section information
            pages_data = self.document_processor.extract_text(file_path, file_extension)

            # Calculate metadata
            total_pages = len(pages_data)

            # Create document record
            document = Document(
                filename=unique_filename,
                originalFilename=original_filename,
                filePath=file_path,
                fileSize=len(file_content),
                fileType=file_extension,
                totalPages=total_pages,
                userId=user_id,
                companyId=company_id,
            )

            db.add(document)
            db.commit()
            db.refresh(document)

            # Get the actual document ID value
            document_id = int(document.id) if document.id is not None else 0

            # Create chunks with page and section metadata
            chunks_data = self.text_chunker.create_chunks(pages_data, document_id)

            # Generate embeddings for chunks
            chunks_with_embeddings = self.embedding_service.generate_chunk_embeddings(
                chunks_data
            )

            # Save chunks to database
            for chunk_data in chunks_with_embeddings:
                chunk = DocumentChunk(
                    documentId=chunk_data["document_id"],
                    chunkText=chunk_data["chunk_text"],
                    chunkIndex=chunk_data["chunk_index"],
                    pageNumber=chunk_data["page_number"],
                    section=chunk_data["section"],
                    embedding=chunk_data["embedding"],
                )
                db.add(chunk)

            # Update document with total chunks count and mark embedding as complete
            db.query(Document).filter(Document.id == document.id).update(
                {
                    Document.totalChunks: len(chunks_with_embeddings),
                    Document.isVectorEmbeddingComplete: True,
                }
            )
            db.commit()

            return {
                "documentId": document.id,
                "filename": original_filename,
                "chunksCreated": len(chunks_with_embeddings),
                "fileSize": len(file_content),
                "status": "success",
            }

        except Exception as e:
            logger.error(f"Error processing document {original_filename}: {e}")
            db.rollback()
            raise

    def get_document_ids_by_user_and_company(
        self, user_id: UUID, company_id: Optional[UUID], db: Session
    ) -> List[Dict[str, Any]]:
        """
        Get document IDs and names filtered by user_id and company_id

        Args:
            user_id: User ID (required)
            company_id: Company ID (optional)
            db: Database session

        Returns:
            List of dictionaries containing document IDs and names
        """
        try:
            # Use explicit casting to handle potential type mismatches
            query = db.query(Document.id, Document.originalFilename).filter(
                cast(Document.userId, String) == str(user_id)
            )

            if company_id is not None:
                # If company_id is provided, filter by both user_id and company_id
                query = query.filter(
                    cast(Document.companyId, String) == str(company_id)
                )

            documents = query.all()
            return [
                {
                    "id": doc.id,
                    "name": doc.originalFilename or "Unknown"
                }
                for doc in documents
            ]

        except Exception as e:
            logger.error(f"Error getting document IDs by user and company: {e}")
            raise

    def search_documents(
        self,
        query: str,
        db: Session,
        user_id: UUID,
        company_id: Optional[UUID] = None,
        top_k: Optional[int] = None,
    ) -> Tuple[List[Dict[str, Any]], List[Chunk]]:
        """
        Search documents using vector similarity, filtered by user_id and company_id

        Args:
            query: Search query
            db: Database session
            user_id: User ID (required)
            company_id: Company ID (optional)
            top_k: Number of top results to return

        Returns:
            Tuple of (documents_info, relevant_chunks)
            - documents_info: List of document dictionaries {id, name}
            - relevant_chunks: List of Chunk objects with metadata
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_service.generate_embedding(query)

            # Get document IDs filtered by user_id and company_id
            documents_info = self.get_document_ids_by_user_and_company(
                user_id, company_id, db
            )

            if not documents_info:
                return ([], [])

            # Extract document IDs for filtering chunks
            document_ids = [doc["id"] for doc in documents_info]

            # Get chunks only from the filtered documents
            chunks = (
                db.query(DocumentChunk)
                .filter(DocumentChunk.documentId.in_(document_ids))
                .all()
            )

            if not chunks:
                return (documents_info, [])

            # Extract embeddings and prepare for similarity search
            chunk_embeddings = []
            valid_chunks = []
            for chunk in chunks:
                if chunk.embedding is not None:
                    # Convert pgvector Vector to list
                    if hasattr(chunk.embedding, "tolist"):
                        embedding_list = chunk.embedding.tolist()
                    elif hasattr(chunk.embedding, "__iter__") and not isinstance(
                        chunk.embedding, str
                    ):
                        embedding_list = list(chunk.embedding)
                    else:
                        embedding_list = chunk.embedding

                    chunk_embeddings.append(embedding_list)
                    valid_chunks.append(chunk)

            if not chunk_embeddings:
                return (documents_info, [])

            # Perform similarity search
            top_indices = self.embedding_service.similarity_search(
                query_embedding, chunk_embeddings, top_k
            )

            # Get relevant chunks with document information
            relevant_chunks = []
            for idx in top_indices:
                chunk = valid_chunks[idx]
                document = (
                    db.query(Document).filter(Document.id == chunk.documentId).first()
                )

                relevant_chunks.append(
                    Chunk(
                        chunk_id=chunk.id,
                        chunk_text=chunk.chunkText,
                        chunk_index=chunk.chunkIndex,
                        document_id=chunk.documentId,
                        document_name=document.originalFilename if document else "Unknown",
                        page_number=chunk.pageNumber,
                        section=chunk.section,
                    )
                )

            return (documents_info, relevant_chunks)

        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            raise

    def get_document(self, document_id: int, db: Session) -> Optional[Document]:
        """Get document by ID"""
        return db.query(Document).filter(Document.id == document_id).first()

    def get_document_chunks(self, document_id: int, db: Session) -> List[DocumentChunk]:
        """Get all chunks for a document"""
        return (
            db.query(DocumentChunk)
            .filter(DocumentChunk.documentId == document_id)
            .all()
        )

    def list_documents(self, db: Session) -> List[Document]:
        """List all documents"""
        return db.query(Document).order_by(Document.uploadedAt.desc()).all()

    def has_completed_embeddings(
        self,
        user_id: UUID,
        company_id: Optional[UUID] = None,
        db: Optional[Session] = None,
    ) -> bool:
        """
        Check if a user has documents with completed vector embeddings

        Args:
            user_id: User ID (required)
            company_id: Company ID (optional) - if provided, only check company documents
            db: Database session

        Returns:
            True if user has at least one document with completed embeddings, False otherwise
        """
        try:
            if db is None:
                return False

            # Use a simpler approach with count
            query = db.query(Document).filter(
                Document.isVectorEmbeddingComplete == True
            )

            # Handle user_id comparison with explicit casting
            query = query.filter(cast(Document.userId, String) == str(user_id))

            if company_id is not None:
                # If company_id is provided, only check company documents
                query = query.filter(
                    cast(Document.companyId, String) == str(company_id)
                )

            # Check if any documents exist with completed embeddings
            count = query.count()
            return count > 0

        except Exception as e:
            logger.error(f"Error checking completed embeddings for user {user_id}: {e}")
            return False
