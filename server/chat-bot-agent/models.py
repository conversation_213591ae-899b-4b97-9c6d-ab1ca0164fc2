from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    DateTime,
    ForeignKey,
    Float,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, relationship
from sqlalchemy.sql import func
from pgvector.sqlalchemy import Vector
from config import config


class Base(DeclarativeBase):
    pass


class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    userId = Column(UUID(as_uuid=True), nullable=False)
    companyId = Column(UUID(as_uuid=True), nullable=True)
    filename = Column(String, nullable=False)
    originalFilename = Column(String, nullable=False)
    filePath = Column(String, nullable=False)
    fileSize = Column(Integer, nullable=False)
    fileType = Column(String, nullable=False)
    documentType = Column(
        String, nullable=False, default="Standard"
    )  # Standard or Compliance
    uploadedAt = Column(DateTime(timezone=True), server_default=func.now())
    totalPages = Column(Integer, nullable=True)
    totalChunks = Column(Integer, nullable=True)
    isVectorEmbeddingComplete = Column(
        Boolean, nullable=False, default=False, server_default="false"
    )

    # Relationship to chunks
    chunks = relationship(
        "DocumentChunk", back_populates="document", cascade="all, delete-orphan"
    )


class DocumentChunk(Base):
    __tablename__ = "document_chunks"

    id = Column(Integer, primary_key=True, index=True)
    documentId = Column(Integer, ForeignKey("documents.id"), nullable=False)
    chunkText = Column(Text, nullable=False)
    chunkIndex = Column(Integer, nullable=False)
    pageNumber = Column(Integer)
    section = Column(String)
    embedding = Column(
        Vector(config.VECTOR_DIMENSION)
    )  # Use config value for dimensions

    # Relationship to document
    document = relationship("Document", back_populates="chunks")


class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, index=True)
    sessionId = Column(String, unique=True, index=True)
    userId = Column(
        UUID(as_uuid=True), nullable=False, index=True
    )  # User ID for filtering
    companyId = Column(
        UUID(as_uuid=True), nullable=True, index=True
    )  # Company ID for filtering (optional)
    name = Column(String, nullable=True)  # Name extracted from first question
    createdAt = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to messages
    messages = relationship(
        "ChatMessage", back_populates="session", cascade="all, delete-orphan"
    )


class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    sessionId = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    messageType = Column(String, nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # NEW FIELDS FOR CONVERSATION AWARENESS
    messageEmbedding = Column(
        Vector(config.VECTOR_DIMENSION)
    )  # Embedding of this message
    queryType = Column(String)  # 'document', 'chat', 'system', 'general'
    confidence = Column(String)  # 'high', 'medium', 'low'
    reasoning = Column(Text)  # AI reasoning process (for assistant messages)
    
    # NEW CONTEXT FIELDS FOR CITATIONS
    documentContext = Column(Text, nullable=True)  # JSON string containing document citations and context

    # Relationship to session
    session = relationship("ChatSession", back_populates="messages")
