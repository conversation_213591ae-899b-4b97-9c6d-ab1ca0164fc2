import streamlit as st
import pandas as pd
from datetime import datetime
import uuid
from uuid import UUID
from typing import Dict, List, Optional, Any
import plotly.express as px
import plotly.graph_objects as go

from api_client import APIClient
from config import config


# Page configuration
st.set_page_config(
    page_title=config.PAGE_TITLE,
    page_icon=config.PAGE_ICON,
    layout=config.LAYOUT,
    initial_sidebar_state="expanded"
)

# Initialize API client


@st.cache_resource
def get_api_client():
    return APIClient()


api_client = get_api_client()

# Initialize session state
if 'sessionId' not in st.session_state:
    st.session_state.sessionId = str(uuid.uuid4())
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'documents' not in st.session_state:
    st.session_state.documents = []
if 'userId' not in st.session_state:
    st.session_state.userId = UUID("001bd935-141d-44ee-abe1-3dd09c44e526")
if 'companyId' not in st.session_state:
    st.session_state.companyId = None


def check_api_health():
    """Check if the API is running"""
    try:
        health_data = api_client.health_check()
        return True, health_data
    except Exception as e:
        return False, str(e)


def upload_document_section():
    """Document upload section"""
    st.header("📄 Document Upload")
    st.markdown(
        "Upload your compliance documents for analysis. You can select multiple files.")

    uploaded_files = st.file_uploader(
        "Choose files",
        type=config.SUPPORTED_FILE_TYPES,
        help=f"Supported formats: {', '.join(config.SUPPORTED_FILE_TYPES).upper()}",
        accept_multiple_files=True
    )

    if uploaded_files:
        # Document type selection
        document_type = st.selectbox(
            "Document Type",
            options=["Standard", "Compliance"],
            help="Select the type of document being uploaded",
            index=0
        )

        # Check file sizes
        oversized = [f for f in uploaded_files if len(
            f.getvalue()) / (1024 * 1024) > config.MAX_FILE_SIZE_MB]
        if oversized:
            st.error(
                f"Some files exceed the maximum allowed size ({config.MAX_FILE_SIZE_MB} MB): " + ", ".join(f.name for f in oversized))
            return

        st.info(f"Selected files: {', '.join(f.name for f in uploaded_files)}")
        st.info(f"Document type: {document_type}")

        if st.button("Upload All", type="primary"):
            with st.spinner("Uploading and queuing documents..."):
                try:
                    files_data = [(f.getvalue(), f.name)
                                  for f in uploaded_files]
                    result = api_client.upload_multiple_documents(
                        files_data,
                        userId=st.session_state.userId,
                        companyId=st.session_state.companyId,
                        documentType=document_type
                    )
                    st.success(
                        f"✅ {result['totalCount']} document(s) uploaded and queued!")
                    st.json(result)
                    # Show a table of upload results
                    if 'uploads' in result:
                        st.subheader("Upload Status")
                        status_table = [
                            {
                                'Filename': r['filename'],
                                'Status': r['status'],
                                'Task ID': r['taskId'],
                                'Message': r['message']
                            } for r in result['uploads']
                        ]
                        st.table(status_table)
                    # Refresh documents list
                    st.session_state.documents = []
                except Exception as e:
                    st.error(f"❌ Upload failed: {e}")


def chat_section():
    """Chat interface section"""
    # Ensure we stay on the Chat page
    if 'page' in st.session_state and st.session_state.page != "Chat":
        st.session_state.page = "Chat"

    st.header("\U0001F4AC Chat with Documents")

    # Chat mode information
    if st.session_state.get('enhanced_mode', True):
        st.info("🤖 **Enhanced Chat Mode**: Using conversation context awareness for better responses")
    else:
        st.info("💬 **Standard Chat Mode**: Basic document-based responses")

    # Debug info (can be removed later)
    if st.checkbox("Show Debug Info", key="debug_info"):
        st.write("**Debug Information:**")
        st.write(f"Current page: {st.session_state.get('page', 'Not set')}")
        st.write(f"Session ID: {st.session_state.get('sessionId', 'Not set')}")
        st.write(
            f"Agentic mode: {st.session_state.get('agentic_mode', False)}")
        st.write(
            f"Last session ID: {st.session_state.get('last_session_id', 'Not set')}")

    # Current session info
    current_session_id = st.session_state.sessionId

    # Get session name if available
    session_name = "New Chat"
    if current_session_id:
        try:
            session_info = api_client.get_session_info(current_session_id)
            session_name = session_info.get('name', 'New Chat')
        except Exception:
            session_name = "New Chat"

    st.info(f"**Current Session:** {session_name}")
    if session_name != "New Chat":
        st.caption(f"Session ID: {current_session_id}")

    # Auto-load chat history when session changes
    if 'last_session_id' not in st.session_state:
        st.session_state.last_session_id = None

    if current_session_id != st.session_state.last_session_id:
        # Session has changed, load chat history
        if current_session_id:
            with st.spinner("Loading chat history..."):
                try:
                    history_data = api_client.get_chat_history(
                        current_session_id)
                    st.session_state.chat_history = history_data.get(
                        'messages', [])
                    st.success(
                        f"✅ Loaded {len(st.session_state.chat_history)} messages from session")
                except Exception as e:
                    st.session_state.chat_history = []
                    st.warning(f"Could not load chat history: {e}")
        else:
            st.session_state.chat_history = []

        st.session_state.last_session_id = current_session_id

    # Session management
    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        session_id = st.text_input(
            "Session ID",
            value=current_session_id,
            help="Unique identifier for this chat session"
        )
    with col2:
        if st.button("New Session"):
            st.session_state.sessionId = str(uuid.uuid4())
            st.session_state.chat_history = []
            st.session_state.last_session_id = None  # Reset to trigger auto-load
            st.rerun()
    with col3:
        if st.button("Switch Sessions"):
            st.session_state.page = "Chat Sessions"
            st.rerun()

    # Display chat name for this session (if available)
    chat_name = None
    if session_id:
        try:
            session_info = api_client.get_session_info(session_id)
            chat_name = session_info.get('name')
        except Exception:
            chat_name = None
    if chat_name:
        st.success(f"**Chat Name:** {chat_name}")

    # Load chat history
    if st.button("Load Chat History"):
        with st.spinner("Loading chat history..."):
            try:
                if session_id:
                    history_data = api_client.get_chat_history(session_id)
                    st.session_state.chat_history = history_data.get(
                        'messages', [])
                    st.success("Chat history loaded!")
                else:
                    st.error("Please enter a session ID")
            except Exception as e:
                st.error(f"Failed to load chat history: {e}")

    # Chat interface
    st.markdown("---")

    # Display chat history
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.chat_history:
            if message['type'] == 'user':
                st.chat_message("user").write(message['content'])
            else:
                st.chat_message("assistant").write(message['content'])

    # Chat input
    query = st.chat_input("Ask a question about your documents...")

    # Chat mode toggle
    use_enhanced = st.checkbox(
        "Use Enhanced Chat",
        help="Enable conversation context awareness",
        value=st.session_state.get('enhanced_mode', True)
    )
    st.session_state.enhanced_mode = use_enhanced

    # Process query
    if query:
        with st.spinner("Processing your question..."):
            try:
                # Choose endpoint based on user preference
                if use_enhanced:
                    response_data = api_client.enhanced_chat_query(
                        query=query,
                        sessionId=session_id,
                        userId=st.session_state.userId,
                        companyId=st.session_state.companyId
                    )
                else:
                    response_data = api_client.chat_query(
                        query=query,
                        sessionId=session_id,
                        userId=st.session_state.userId,
                        companyId=st.session_state.companyId
                    )

                # Display response
                st.chat_message("user").write(query)
                st.chat_message("assistant").write(response_data['response'])

                # Display metrics
                if use_enhanced:
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Confidence", response_data['confidence'])
                    with col2:
                        st.metric("Chunks Used", response_data['chunksUsed'])
                    with col3:
                        if response_data.get('reasoning'):
                            st.metric("Reasoning", "Available")
                    with col4:
                        if response_data.get('contextSources'):
                            sources = response_data['contextSources']
                            total_sources = sources.get('documents', 0) + sources.get('conversation', 0)
                            st.metric("Context Sources", total_sources)
                else:
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Confidence", response_data['confidence'])
                    with col2:
                        st.metric("Chunks Used", response_data['chunksUsed'])
                    with col3:
                        if response_data.get('reasoning'):
                            st.metric("Reasoning", "Available")

                # Display context chunks (backward compatibility)
                if response_data.get('contextChunks'):
                    expander_title = "📄 Document Context Sources" if use_enhanced else "📄 Context Sources"
                    with st.expander(expander_title, expanded=False):
                        if len(response_data['contextChunks']) == 0:
                            context_message = "No relevant document context found" if use_enhanced else "No relevant context found"
                            st.info(context_message)
                        else:
                            for i, chunk in enumerate(response_data['contextChunks'][:3]):
                                with st.container():
                                    chunk_text = chunk.get('chunk_text', '')
                                    if chunk_text:
                                        st.text_area(
                                            f"Chunk {i+1}",
                                            chunk_text,
                                            height=100,
                                            help=f"Page: {chunk.get('page_number', 'N/A')}, Section: {chunk.get('section', 'N/A')}"
                                        )
                                    else:
                                        st.write("No text content")

                                    col1, col2 = st.columns(2)
                                    with col1:
                                        st.write(
                                            f"**Document:** {chunk.get('document_name', 'Unknown')}")
                                    with col2:
                                        st.write(
                                            f"**Page:** {chunk.get('page_number', 'N/A')}")

                            if len(response_data['contextChunks']) > 3:
                                st.info(
                                    f"... and {len(response_data['contextChunks']) - 3} more chunks")

                        # Show raw data for debugging
                        if st.checkbox("Show raw context data"):
                            st.json(response_data['contextChunks'])

                # Display rich document citations (new feature)
                if response_data.get('documentContext') and response_data['documentContext'] is not None:
                    document_context = response_data['documentContext']
                    citations = document_context.citations if hasattr(document_context, 'citations') else []
                    total_docs = document_context.totalDocuments if hasattr(document_context, 'totalDocuments') else 0
                    total_chunks = document_context.totalChunks if hasattr(document_context, 'totalChunks') else 0
                    
                    if citations:
                        with st.expander("📚 Rich Document Citations", expanded=False):
                            st.info(f"📊 Used {total_chunks} chunks from {total_docs} documents")
                            
                            for i, citation in enumerate(citations[:5]):
                                with st.container():
                                    st.markdown(f"**Citation {i+1}:**")
                                    
                                    col1, col2 = st.columns([2, 1])
                                    with col1:
                                        st.write(f"**Document:** {citation.document_name}")
                                    with col2:
                                        st.write(f"**Chunks:** {len(citation.chunks)}")
                                    
                                    # Display chunks for this citation
                                    for j, chunk in enumerate(citation.chunks[:3]):  # Show first 3 chunks
                                        with st.container():
                                            st.write(f"**Chunk {j+1}:**")
                                            if chunk.page_number:
                                                st.write(f"**Page:** {chunk.page_number}")
                                            if chunk.section:
                                                st.write(f"**Section:** {chunk.section}")
                                            
                                            st.text_area(
                                                f"Text Excerpt {i+1}-{j+1}",
                                                chunk.chunk_text,
                                                height=80,
                                                key=f"citation_text_{i}_{j}"
                                            )
                                    
                                    if len(citation.chunks) > 3:
                                        st.info(f"... and {len(citation.chunks) - 3} more chunks in this document")
                                    
                                    if i < len(citations) - 1:
                                        st.divider()
                            
                            if len(citations) > 5:
                                st.info(f"... and {len(citations) - 5} more citations")



                # Display conversation context (enhanced feature - backward compatibility)
                if use_enhanced and response_data.get('conversationContext'):
                    with st.expander("💬 Conversation Context (Legacy)", expanded=False):
                        if len(response_data['conversationContext']) == 0:
                            st.info("No relevant conversation context found")
                        else:
                            st.info(f"Found {len(response_data['conversationContext'])} relevant previous messages")
                            
                            for i, ctx in enumerate(response_data['conversationContext'][:5]):
                                with st.container():
                                    col1, col2 = st.columns([3, 1])
                                    with col1:
                                        st.write(f"**{ctx.get('messageType', 'Unknown').title()}:** {ctx.get('content', 'No content')[:200]}...")
                                    with col2:
                                        st.write(f"**Type:** {ctx.get('queryType', 'N/A')}")
                                        st.write(f"**Confidence:** {ctx.get('confidence', 'N/A')}")
                                    
                                    st.caption(f"Timestamp: {ctx.get('timestamp', 'N/A')}")
                                    
                                    if i < len(response_data['conversationContext']) - 1:
                                        st.divider()

                            if len(response_data['conversationContext']) > 5:
                                st.info(
                                    f"... and {len(response_data['conversationContext']) - 5} more conversation references")

                        # Show raw data for debugging
                        if st.checkbox("Show raw conversation data"):
                            st.json(response_data['conversationContext'])

                # Display context sources breakdown
                if use_enhanced and response_data.get('contextSources'):
                    with st.expander("📊 Context Sources Breakdown", expanded=False):
                        sources = response_data['contextSources']
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Document Sources", sources.get('documents', 0))
                        with col2:
                            st.metric("Conversation Sources", sources.get('conversation', 0))
                        
                        # Create a simple bar chart
                        if sources.get('documents', 0) > 0 or sources.get('conversation', 0) > 0:
                            import plotly.express as px
                            source_data = {
                                'Source Type': ['Documents', 'Conversation'],
                                'Count': [sources.get('documents', 0), sources.get('conversation', 0)]
                            }
                            fig = px.bar(source_data, x='Source Type', y='Count', 
                                        title='Context Sources Used',
                                        color='Source Type',
                                        color_discrete_map={'Documents': '#1f77b4', 'Conversation': '#ff7f0e'})
                            st.plotly_chart(fig, use_container_width=True)

                # Update session ID if it changed
                st.session_state.sessionId = response_data['sessionId']

                # Refresh chat history
                st.rerun()

            except Exception as e:
                st.error(f"❌ Error processing query: {e}")


def documents_section():
    """Documents management section"""
    st.header("📚 Documents")
    st.markdown("View and manage your uploaded documents")

    try:
        # Get documents
        documents_data = api_client.list_documents(
            userId=st.session_state.userId,
            companyId=st.session_state.companyId
        )

        if not documents_data.get('documents'):
            st.info("No documents found. Upload some documents to get started!")
            return

        documents = documents_data['documents']

        # Create DataFrame for analysis
        df = pd.DataFrame(documents)
        df['uploadedAt'] = pd.to_datetime(df['uploadedAt'])
        df['fileSizeMb'] = df['fileSize'] / (1024 * 1024)

        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Documents", len(documents))
        with col2:
            st.metric("Total Chunks", df['chunksCount'].sum())
        with col3:
            st.metric("Total Size", f"{df['fileSizeMb'].sum():.2f} MB")
        with col4:
            st.metric("Avg Size", f"{df['fileSizeMb'].mean():.2f} MB")

        # Display documents table
        st.subheader("📋 Document List")

        # Prepare display data
        display_df = df.copy()
        display_df['uploadedAt'] = display_df['uploadedAt'].dt.strftime(
            '%Y-%m-%d %H:%M')
        display_df['fileSize'] = display_df['fileSizeMb'].apply(
            lambda x: f"{x:.2f} MB")
        display_df = display_df[['originalFilename', 'fileType',
                                 'documentType', 'fileSize', 'chunksCount', 'uploadedAt', 'isVectorEmbeddingComplete']]

        st.dataframe(
            display_df,
            column_config={
                "originalFilename": "Filename",
                "fileType": "Type",
                "documentType": "Document Type",
                "fileSize": "Size",
                "chunksCount": "Chunks",
                "uploadedAt": "Uploaded",
                "isVectorEmbeddingComplete": st.column_config.CheckboxColumn("Embedding Complete")
            },
            hide_index=True
        )

        # Document details section
        st.subheader("📄 Document Details")
        selected_doc = st.selectbox(
            "Select a document to view details:",
            options=documents,
            format_func=lambda x: f"{x['originalFilename']} (ID: {x['id']})"
        )

        if selected_doc:
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Document ID:** {selected_doc['id']}")
                st.write(
                    f"**Original Filename:** {selected_doc['originalFilename']}")
                st.write(f"**File Type:** {selected_doc['fileType']}")
                st.write(
                    f"**Document Type:** {selected_doc.get('documentType', 'Standard')}")
                st.write(
                    f"**File Size:** {selected_doc['fileSize'] / (1024*1024):.2f} MB")

            with col2:
                st.write(
                    f"**Total Pages:** {selected_doc.get('totalPages', 'N/A')}")
                st.write(
                    f"**Total Chunks:** {selected_doc.get('totalChunks', 'N/A')}")
                st.write(
                    f"**Chunks Count:** {selected_doc.get('chunksCount', 'N/A')}")
                st.write(f"**Uploaded:** {selected_doc['uploadedAt']}")
                st.write(f"**Internal Filename:** {selected_doc['filename']}")
                
                # Show embedding status with visual indicator
                embedding_status = selected_doc.get('isVectorEmbeddingComplete', False)
                if embedding_status:
                    st.success("✅ Vector Embedding Complete")
                else:
                    st.warning("⏳ Vector Embedding Pending")

        # Visualizations
        st.subheader("📊 Document Analytics")

        col1, col2 = st.columns(2)

        with col1:
            # File type distribution
            fig_pie = px.pie(df, names='fileType',
                             title='File Types Distribution')
            st.plotly_chart(fig_pie, use_container_width=True)

        with col2:
            # Document type distribution
            fig_doc_pie = px.pie(df, names='documentType',
                                 title='Document Types Distribution')
            st.plotly_chart(fig_doc_pie, use_container_width=True)

        col3, col4 = st.columns(2)

        with col3:
            # File size distribution
            fig_hist = px.histogram(
                df, x='fileSizeMb', title='File Size Distribution (MB)')
            st.plotly_chart(fig_hist, use_container_width=True)

        with col4:
            # Chunks distribution
            fig_chunks = px.histogram(
                df, x='chunksCount', title='Chunks Distribution')
            st.plotly_chart(fig_chunks, use_container_width=True)

        # Upload timeline
        fig_timeline = px.line(df, x='uploadedAt', y='fileSizeMb',
                               title='Document Upload Timeline',
                               labels={'uploadedAt': 'Upload Date', 'fileSizeMb': 'File Size (MB)'})
        st.plotly_chart(fig_timeline, use_container_width=True)

    except Exception as e:
        st.error(f"❌ Error loading documents: {e}")


def chat_sessions_section():
    """Chat sessions management section"""
    st.header("💬 Chat Sessions")
    st.markdown("View and manage your chat sessions")

    # Filter options
    st.subheader("🔍 Filter Options")
    col1, col2, col3 = st.columns(3)

    with col1:
        filter_user_id = st.text_input(
            "Filter by User ID",
            value="",
            help="Enter user ID to filter sessions"
        )

    with col2:
        filter_company_id = st.text_input(
            "Filter by Company ID",
            value="",
            help="Enter company ID to filter sessions"
        )

    with col3:
        if st.button("Apply Filters"):
            st.session_state.filter_user_id = filter_user_id if filter_user_id else None
            st.session_state.filter_company_id = filter_company_id if filter_company_id else None
            st.rerun()

    try:
        # Get sessions with filters
        user_id_filter = None
        company_id_filter = None

        if hasattr(st.session_state, 'filter_user_id') and st.session_state.filter_user_id:
            try:
                user_id_filter = UUID(st.session_state.filter_user_id)
            except ValueError:
                st.error("Invalid User ID format")
                return

        if hasattr(st.session_state, 'filter_company_id') and st.session_state.filter_company_id:
            try:
                company_id_filter = UUID(st.session_state.filter_company_id)
            except ValueError:
                st.error("Invalid Company ID format")
                return

        sessions_data = api_client.list_sessions(
            userId=user_id_filter, companyId=company_id_filter)
        sessions = sessions_data.get('sessions', [])

        if not sessions:
            st.info(
                "No chat sessions found with the current filters. Start a conversation to create sessions!")
            return

        # Store sessions in session state for easy access
        st.session_state.sessions = sessions

        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Sessions", len(sessions))
        with col2:
            total_messages = sum(session.get('messageCount', 0)
                                 for session in sessions)
            st.metric("Total Messages", total_messages)
        with col3:
            active_sessions = len(
                [s for s in sessions if s.get('messageCount', 0) > 0])
            st.metric("Active Sessions", active_sessions)
        with col4:
            unique_users = len(set(session.get('userId')
                               for session in sessions if session.get('userId')))
            st.metric("Unique Users", unique_users)

        # Display sessions
        st.subheader("📋 Session List")

        for session in sessions:
            with st.container():
                col1, col2, col3 = st.columns([4, 1, 1])

                with col1:
                    st.write(f"**Session ID:** {session['sessionId']}")
                    st.write(
                        f"**Name:** {session.get('name', 'Unnamed Session')}")
                    st.write(f"**User ID:** {session.get('userId', 'N/A')}")
                    if session.get('companyId'):
                        st.write(f"**Company ID:** {session['companyId']}")
                    st.write(
                        f"**Created:** {session['createdAt'][:19].replace('T', ' ')}")
                    st.write(
                        f"**Last Activity:** {session['lastActivity'][:19].replace('T', ' ')}")

                with col2:
                    st.metric("Messages", session.get('messageCount', 0))

                with col3:
                    if st.button("Load Session", key=f"load_{session['sessionId']}"):
                        st.session_state.sessionId = session['sessionId']
                        st.session_state.page = "Chat"

                        # Load chat history
                        history_data = api_client.get_chat_history(
                            session['sessionId'])
                        st.session_state.chat_history = history_data.get(
                            'messages', [])
                        st.rerun()

                st.divider()

        # Session management
        st.subheader("🔄 Session Management")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("Create New Session"):
                st.session_state.sessionId = str(uuid.uuid4())
                st.session_state.chat_history = []
                st.session_state.last_session_id = None  # Reset to trigger auto-load
                st.session_state.page = "Chat"
                st.rerun()

        with col2:
            if st.button("Clear Filters"):
                if hasattr(st.session_state, 'filter_user_id'):
                    del st.session_state.filter_user_id
                if hasattr(st.session_state, 'filter_company_id'):
                    del st.session_state.filter_company_id
                st.rerun()

        with col3:
            if st.button("Clear All Sessions"):
                st.warning("This would clear all sessions (not implemented)")

    except Exception as e:
        st.error(f"❌ Error loading sessions: {e}")


def session_info_section():
    """Session information section"""
    st.header("ℹ️ Session Information")

    # Session ID input
    session_id = st.text_input(
        "Enter Session ID", value=st.session_state.sessionId)

    if session_id:
        try:
            session_info = api_client.get_session_info(session_id)

            st.success("✅ Session found!")

            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Session ID:** {session_info['sessionId']}")
                st.write(f"**Name:** {session_info.get('name', 'Unnamed')}")
                st.write(f"**User ID:** {session_info.get('userId', 'N/A')}")
            with col2:
                st.write(f"**Created:** {session_info['createdAt']}")
                st.write(f"**Messages:** {session_info['messageCount']}")
                if session_info.get('companyId'):
                    st.write(f"**Company ID:** {session_info['companyId']}")

            # Load session button
            if st.button("Load This Session"):
                st.session_state.sessionId = session_id
                st.session_state.page = "Chat"
                st.rerun()

        except Exception as e:
            st.error(f"❌ Session not found or error: {e}")


def main():
    """Main application"""
    # Check API health
    api_healthy, health_data = check_api_health()

    if not api_healthy:
        st.error("❌ API is not running. Please start the API server first.")
        st.json(health_data)
        return

    # Sidebar navigation
    st.sidebar.title("🧭 Navigation")

    # User settings
    with st.sidebar.expander("⚙️ User Settings", expanded=False):
        user_id_input = st.text_input(
            "User ID",
            value=str(st.session_state.userId),
            help="Enter your user ID"
        )
        company_id_input = st.text_input(
            "Company ID (optional)",
            value=str(
                st.session_state.companyId) if st.session_state.companyId else "",
            help="Enter your company ID (optional)"
        )

        if st.button("Update Settings"):
            try:
                st.session_state.userId = UUID(
                    user_id_input) if user_id_input else None
                st.session_state.companyId = UUID(
                    company_id_input) if company_id_input else None
                st.success("Settings updated!")
            except ValueError:
                st.error("Invalid UUID format")

        # Validate settings
        if not st.session_state.userId:
            st.warning("⚠️ User ID is required")

    # Page navigation
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Chat", "Upload Documents", "Documents", "Chat Sessions", "Session Info"],
        index=0
    )

    # Store current page
    st.session_state.page = page

    # Display current page
    if page == "Chat":
        chat_section()
    elif page == "Upload Documents":
        upload_document_section()
    elif page == "Documents":
        documents_section()
    elif page == "Chat Sessions":
        chat_sessions_section()
    elif page == "Session Info":
        session_info_section()


if __name__ == "__main__":
    main()
