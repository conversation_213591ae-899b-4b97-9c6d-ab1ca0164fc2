import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    def __init__(self):
        # API Configuration
        self.API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8080")
        
        # API Endpoints
        self.UPLOAD_ENDPOINT = f"{self.API_BASE_URL}/upload"
        self.MULTIPLE_UPLOAD_ENDPOINT = f"{self.API_BASE_URL}/upload/multiple"
        self.UPLOAD_STATUS_ENDPOINT = f"{self.API_BASE_URL}/upload/status"
        self.QUEUE_STATUS_ENDPOINT = f"{self.API_BASE_URL}/upload/queue/status"
        self.CHAT_ENDPOINT = f"{self.API_BASE_URL}/chat"
        self.CHAT_HISTORY_ENDPOINT = f"{self.API_BASE_URL}/chat/history"
        self.SESSION_INFO_ENDPOINT = f"{self.API_BASE_URL}/chat/session"
        self.SESSIONS_ENDPOINT = f"{self.API_BASE_URL}/chat/sessions"
        self.DOCUMENTS_ENDPOINT = f"{self.API_BASE_URL}/documents"
        self.HEALTH_ENDPOINT = f"{self.API_BASE_URL}/health"
        
        # UI Configuration
        self.PAGE_TITLE = "LE Compliance ChatBot"
        self.PAGE_ICON = "🤖"
        self.LAYOUT = "wide"
        
        # File Upload Configuration
        self.MAX_FILE_SIZE_MB = 31457280
        self.SUPPORTED_FILE_TYPES = [
            "pdf", "docx", "doc", "pptx", "ppt", "xlsx", "xls"
        ]
        
        # Chat Configuration
        self.MAX_MESSAGE_LENGTH = 1000
        self.DEFAULT_SESSION_ID = "default"

config = Config() 