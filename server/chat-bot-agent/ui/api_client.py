import requests
import json
from typing import Dict, List, Optional, Any
from uuid import UUID
from config import config

class APIClient:
    """Client for interacting with the LE Compliance ChatBot API"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response and raise exceptions for errors"""
        if response.status_code >= 400:
            try:
                error_data = response.json()
                raise Exception(error_data.get('detail', f'HTTP {response.status_code}'))
            except json.JSONDecodeError:
                raise Exception(f'HTTP {response.status_code}: {response.text}')
        
        return response.json()
    
    def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        try:
            response = self.session.get(config.HEALTH_ENDPOINT)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Health check failed: {e}")
    
    def upload_document(self, file_data: bytes, filename: str, userId: UUID, companyId: Optional[UUID] = None, documentType: Optional[str] = "Standard") -> Dict[str, Any]:
        """Upload a document"""
        try:
            files = {'file': (filename, file_data)}
            data = {'user_id': str(userId), 'document_type': documentType}
            if companyId:
                data['company_id'] = str(companyId)
            
            # Create a temporary session without Content-Type header for file upload
            temp_session = requests.Session()
            temp_session.headers.update({
                'Accept': 'application/json'
            })
            response = temp_session.post(config.UPLOAD_ENDPOINT, files=files, data=data)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Upload failed: {e}")
    
    def upload_multiple_documents(self, files_data: List[tuple], userId: UUID, companyId: Optional[UUID] = None, documentType: Optional[str] = "Standard") -> Dict[str, Any]:
        """Upload multiple documents"""
        try:
            files = []
            for file_data, filename in files_data:
                files.append(('files', (filename, file_data)))
            
            data = {'user_id': str(userId), 'document_type': documentType}
            if companyId:
                data['company_id'] = str(companyId)
            
            # Create a temporary session without Content-Type header for file upload
            temp_session = requests.Session()
            temp_session.headers.update({
                'Accept': 'application/json'
            })
            response = temp_session.post(config.MULTIPLE_UPLOAD_ENDPOINT, files=files, data=data)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Multiple upload failed: {e}")
    
    def get_upload_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a document processing task"""
        try:
            url = f"{config.UPLOAD_STATUS_ENDPOINT}/{task_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to get upload status: {e}")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get the overall status of the document processing queue"""
        try:
            response = self.session.get(config.QUEUE_STATUS_ENDPOINT)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to get queue status: {e}")
    
    def chat_query(self, query: str, userId: UUID, companyId: Optional[UUID] = None, sessionId: Optional[str] = None) -> Dict[str, Any]:
        """Send a chat query"""
        try:
            data = {
                'query': query,
                'userId': str(userId),
            }
            if sessionId:
                data['sessionId'] = sessionId
            if companyId:
                data['companyId'] = str(companyId)
            
            response = self.session.post(config.CHAT_ENDPOINT, json=data)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Chat query failed: {e}")
    
    def enhanced_chat_query(self, query: str, userId: UUID, companyId: Optional[UUID] = None, sessionId: Optional[str] = None) -> Dict[str, Any]:
        """Send an enhanced chat query with conversation context awareness"""
        try:
            data = {
                'query': query,
                'userId': str(userId),
            }
            if sessionId:
                data['sessionId'] = sessionId
            if companyId:
                data['companyId'] = str(companyId)
            
            response = self.session.post(config.CHAT_ENDPOINT, json=data)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Enhanced chat query failed: {e}")
    
    def get_chat_history(self, session_id: str) -> Dict[str, Any]:
        """Get chat history for a session"""
        try:
            url = f"{config.CHAT_HISTORY_ENDPOINT}/{session_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to get chat history: {e}")
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """Get session information"""
        try:
            url = f"{config.SESSION_INFO_ENDPOINT}/{session_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to get session info: {e}")
    
    def list_documents(self, userId: Optional[UUID] = None, companyId: Optional[UUID] = None) -> Dict[str, Any]:
        """List all uploaded documents"""
        try:
            params = {}
            if userId:
                params['user_id'] = str(userId)
            if companyId:
                params['company_id'] = str(companyId)
            
            response = self.session.get(config.DOCUMENTS_ENDPOINT, params=params)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to list documents: {e}")
    
    def get_document(self, document_id: int) -> Dict[str, Any]:
        """Get a single document by ID"""
        try:
            url = f"{config.DOCUMENTS_ENDPOINT}/{document_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to get document: {e}")
    
    def list_sessions(self, userId: Optional[UUID] = None, companyId: Optional[UUID] = None) -> Dict[str, Any]:
        """List chat sessions with optional filtering by user_id and/or company_id"""
        try:
            params = {}
            if userId:
                params['user_id'] = str(userId)
            if companyId:
                params['company_id'] = str(companyId)
            
            response = self.session.get(config.SESSIONS_ENDPOINT, params=params)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to list sessions: {e}")
    
    def list_sessions_by_user(self, user_id: UUID) -> Dict[str, Any]:
        """List chat sessions for a specific user"""
        try:
            url = f"{config.SESSIONS_ENDPOINT}/user/{user_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to list sessions for user: {e}")
    
    def list_sessions_by_company(self, company_id: UUID) -> Dict[str, Any]:
        """List chat sessions for a specific company"""
        try:
            url = f"{config.SESSIONS_ENDPOINT}/company/{company_id}"
            response = self.session.get(url)
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"Failed to list sessions for company: {e}") 