#!/usr/bin/env python3
"""
Launcher script for the LE Compliance ChatBot Streamlit UI
"""

import subprocess
import sys
import os

def main():
    """Launch the Streamlit application"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Change to the UI directory
    os.chdir(script_dir)
    
    # Check if requirements are installed
    try:
        import streamlit
        import pandas
        import plotly
        import requests
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("📦 Installing requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    
    # Launch Streamlit
    print("🚀 Starting Streamlit application...")
    print("📱 The UI will open in your browser at http://localhost:8501")
    print("🔗 Make sure the FastAPI backend is running on http://localhost:8080")
    print("⏹️  Press Ctrl+C to stop the application")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")

if __name__ == "__main__":
    main() 