# LE Compliance ChatBot - Streamlit UI

A modern web interface for the LE Compliance ChatBot built with Streamlit.

## Features

- 📄 **Document Upload**: Upload and process compliance documents
- 💬 **Chat Interface**: Interactive chat with AI about your documents
- 📚 **Document Management**: View and analyze uploaded documents
- 📊 **Session Management**: Track chat sessions and history
- 📈 **Analytics**: Visual charts and metrics for document analysis

## Prerequisites

1. **Python 3.8+** installed
2. **FastAPI Backend** running on `http://localhost:8080`
3. **Database** properly configured and running

## Installation

1. **Install UI dependencies**:
   ```bash
   cd ui
   pip install -r requirements.txt
   ```

2. **Start the FastAPI backend** (from the main project directory):
   ```bash
   cd ..
   uvicorn main:app --reload --host 0.0.0.0 --port 8080
   ```

3. **Launch the Streamlit UI**:
   ```bash
   cd ui
   python run.py
   ```

   Or manually:
   ```bash
   streamlit run app.py --server.port 8501
   ```

## Usage

### 1. Document Upload
- Navigate to "Document Upload" in the sidebar
- Select a file (PDF, DOCX, PPTX, XLSX, etc.)
- Click "Upload" to process the document
- The document will be chunked and indexed for AI analysis

### 2. Chat Interface
- Go to "Chat" in the sidebar
- Enter your questions about the uploaded documents
- Toggle "Agentic Mode" for advanced reasoning
- View response details including context chunks used

### 3. Document Management
- Visit "Document Management" to see all uploaded documents
- View analytics including file type distribution and upload timeline
- Monitor document processing metrics

### 4. Session Information
- Check "Session Info" to view chat session details
- Track message counts and session creation times

## Configuration

Edit `ui/config.py` to customize:
- API base URL
- File upload limits
- Supported file types
- UI settings

## API Endpoints Used

The UI integrates with all FastAPI endpoints:
- `POST /upload` - Document upload
- `POST /chat` - Send chat queries
- `GET /chat/history/{session_id}` - Get chat history
- `GET /chat/session/{session_id}` - Get session info
- `GET /documents` - List all documents
- `GET /health` - Health check

## Troubleshooting

### API Connection Issues
- Ensure the FastAPI backend is running on `http://localhost:8080`
- Check that the database is properly configured
- Verify all environment variables are set

### File Upload Issues
- Check file size limits (default: 10MB)
- Ensure file type is supported
- Verify database connection for document storage

### Chat Issues
- Make sure documents are uploaded before chatting
- Check that the AI service (Google Gemini) is configured
- Verify session management is working

## Development

To modify the UI:
1. Edit `ui/app.py` for main functionality
2. Update `ui/api_client.py` for API integration changes
3. Modify `ui/config.py` for configuration changes
4. Update `ui/requirements.txt` for new dependencies

## License

Same as the main project. 