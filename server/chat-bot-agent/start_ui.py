#!/usr/bin/env python3
"""
Startup script for the LE Compliance ChatBot Streamlit UI
Run this from the main project directory
"""

import subprocess
import sys
import os

def main():
    """Start the Streamlit UI"""
    # Get the current directory (main project directory)
    current_dir = os.getcwd()
    ui_dir = os.path.join(current_dir, "ui")
    
    # Check if UI directory exists
    if not os.path.exists(ui_dir):
        print("❌ UI directory not found. Please ensure the 'ui' folder exists.")
        return
    
    # Change to UI directory and run the launcher
    os.chdir(ui_dir)
    
    print("🚀 Starting LE Compliance ChatBot UI...")
    print(f"📁 UI Directory: {ui_dir}")
    print("🔗 Make sure the FastAPI backend is running on http://localhost:8080")
    
    try:
        # Run the UI launcher
        subprocess.run([sys.executable, "run.py"])
    except KeyboardInterrupt:
        print("\n👋 UI stopped by user")
    except Exception as e:
        print(f"❌ Error starting UI: {e}")

if __name__ == "__main__":
    main() 