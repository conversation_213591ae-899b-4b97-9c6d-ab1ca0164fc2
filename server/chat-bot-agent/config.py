import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Database Configuration
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://username:password@localhost:5432/chatbot_db")
    
    # Google Gemini API
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
    
    # File Storage - Use absolute path for VPS deployment
    UPLOAD_DIR = os.getenv("UPLOAD_DIR", "/var/www/lecompliance/server/chat-bot-agent/uploads")
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "31457280"))  # 30MB default (30 * 1024 * 1024)
    
    # Vector Database - Updated for BAAI/bge-large-en-v1.5 model
    VECTOR_DIMENSION = int(os.getenv("VECTOR_DIMENSION", "1024"))  # Changed to 1024 for BAAI/bge-large-en-v1.5
    SIMILARITY_TOP_K = int(os.getenv("SIMILARITY_TOP_K", "10"))
    
    # Embedding Model Configuration
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "BAAI/bge-large-en-v1.5")
    FORCE_CPU = os.getenv("FORCE_CPU", "true").lower() == "true"  # Force CPU usage to avoid MPS memory issues
    
    # Chunking Configuration
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "500"))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "50"))
    
    # Supported file types
    SUPPORTED_EXTENSIONS = {
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.doc': 'application/msword',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel'
    }

config = Config() 