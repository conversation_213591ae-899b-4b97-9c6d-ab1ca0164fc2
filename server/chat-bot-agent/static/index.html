<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LE Compliance ChatBot</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.1em;
        opacity: 0.9;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 0;
        min-height: 600px;
      }

      .sidebar {
        background: #f8f9fa;
        padding: 30px;
        border-right: 1px solid #e9ecef;
      }

      .upload-section {
        margin-bottom: 30px;
      }

      .upload-section h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.3em;
      }

      .file-upload {
        border: 2px dashed #3498db;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
      }

      .file-upload:hover {
        border-color: #2980b9;
        background: #f7f9fc;
      }

      .file-upload.dragover {
        border-color: #27ae60;
        background: #e8f5e8;
      }

      .file-upload input[type="file"] {
        display: none;
      }

      .upload-icon {
        font-size: 3em;
        color: #3498db;
        margin-bottom: 15px;
      }

      .upload-text {
        color: #7f8c8d;
        font-size: 1.1em;
      }

      .supported-formats {
        margin-top: 20px;
        font-size: 0.9em;
        color: #95a5a6;
      }

      .config-section {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .config-section h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
      }

      .config-item {
        margin-bottom: 10px;
      }

      .config-item label {
        display: block;
        margin-bottom: 5px;
        color: #495057;
        font-size: 12px;
        font-weight: 500;
      }

      .config-item input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 12px;
        box-sizing: border-box;
      }

      .config-item input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .documents-list {
        margin-top: 30px;
      }

      .documents-list h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.3em;
      }

      .document-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 4px solid #3498db;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .document-name {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
      }

      .document-meta {
        font-size: 0.9em;
        color: #7f8c8d;
      }

      .chat-section {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .chat-header {
        background: #ecf0f1;
        padding: 20px 30px;
        border-bottom: 1px solid #e9ecef;
      }

      .chat-header h3 {
        color: #2c3e50;
        font-size: 1.3em;
      }

      .chat-messages {
        flex: 1;
        padding: 20px 30px;
        overflow-y: auto;
        max-height: 400px;
      }

      .message {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-start;
      }

      .message.user {
        justify-content: flex-end;
      }

      .message-content {
        max-width: 70%;
        padding: 15px 20px;
        border-radius: 20px;
        position: relative;
      }

      .message.user .message-content {
        background: #3498db;
        color: white;
        border-bottom-right-radius: 5px;
      }

      .message.assistant .message-content {
        background: #ecf0f1;
        color: #2c3e50;
        border-bottom-left-radius: 5px;
      }

      .message-time {
        font-size: 0.8em;
        opacity: 0.7;
        margin-top: 5px;
      }

      .chat-input {
        padding: 20px 30px;
        border-top: 1px solid #e9ecef;
        background: white;
      }

      .input-group {
        display: flex;
        gap: 10px;
      }

      .chat-input input {
        flex: 1;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        font-size: 1em;
        outline: none;
        transition: border-color 0.3s ease;
      }

      .chat-input input:focus {
        border-color: #3498db;
      }

      .send-btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 15px 25px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 1em;
        transition: background 0.3s ease;
      }

      .send-btn:hover {
        background: #2980b9;
      }

      .send-btn:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
      }

      .agentic-toggle {
        margin-top: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .toggle-switch {
        position: relative;
        width: 50px;
        height: 24px;
        background: #bdc3c7;
        border-radius: 12px;
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .toggle-switch.active {
        background: #27ae60;
      }

      .toggle-switch::after {
        content: "";
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
      }

      .toggle-switch.active::after {
        transform: translateX(26px);
      }

      .loading {
        display: none;
        text-align: center;
        padding: 20px;
        color: #7f8c8d;
      }

      .loading.show {
        display: block;
      }

      .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        background: #e74c3c;
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
      }

      .success {
        background: #27ae60;
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
      }

      @media (max-width: 768px) {
        .main-content {
          grid-template-columns: 1fr;
        }

        .sidebar {
          border-right: none;
          border-bottom: 1px solid #e9ecef;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>LE Compliance ChatBot</h1>
        <p>
          Upload documents and ask questions about legal and ethical compliance
        </p>
      </div>

      <div class="main-content">
        <div class="sidebar">
          <div class="config-section">
            <h3>Configuration</h3>
            <div class="config-item">
              <label for="userId">User ID (Required):</label>
              <input
                type="text"
                id="userId"
                value="001bd935-141d-44ee-abe1-3dd09c44e526"
                placeholder="Enter User ID"
              />
            </div>
            <div class="config-item">
              <label for="companyId">Company ID (Optional):</label>
              <input
                type="text"
                id="companyId"
                placeholder="Enter Company ID"
              />
            </div>
          </div>

          <div class="upload-section">
            <h3>Upload Documents</h3>
            <div class="file-upload" id="fileUpload">
              <div class="upload-icon">📄</div>
              <div class="upload-text">Click to upload or drag files here</div>
              <input
                type="file"
                id="fileInput"
                accept=".pdf,.docx,.doc,.pptx,.ppt,.xlsx,.xls"
                multiple
              />
            </div>
            <div class="supported-formats">
              Supported: PDF, DOCX, PPTX, XLSX, XLS
            </div>
          </div>

          <div class="documents-list">
            <h3>Uploaded Documents</h3>
            <div id="documentsList">
              <p style="color: #95a5a6; text-align: center">
                No documents uploaded yet
              </p>
            </div>
          </div>
        </div>

        <div class="chat-section">
          <div class="chat-header">
            <h3>Chat with AI Assistant</h3>
          </div>

          <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
              <div class="message-content">
                Hello! I'm your LE Compliance Assistant. Upload some documents
                and ask me questions about their content.
              </div>
            </div>
          </div>

          <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Processing your request...</div>
          </div>

          <div class="chat-input">
            <div class="input-group">
              <input
                type="text"
                id="chatInput"
                placeholder="Ask a question about your documents..."
                maxlength="1000"
              />
              <button class="send-btn" id="sendBtn">Send</button>
            </div>

          </div>
        </div>
      </div>
    </div>

    <script>
      const API_BASE = "http://localhost:8080";
      let sessionId = null;

      // DOM elements
      const fileUpload = document.getElementById("fileUpload");
      const fileInput = document.getElementById("fileInput");
      const documentsList = document.getElementById("documentsList");
      const chatMessages = document.getElementById("chatMessages");
      const chatInput = document.getElementById("chatInput");
      const sendBtn = document.getElementById("sendBtn");
      const loading = document.getElementById("loading");

      const userId = document.getElementById("userId");
      const companyId = document.getElementById("companyId");

      // File upload handling
      fileUpload.addEventListener("click", () => fileInput.click());
      fileUpload.addEventListener("dragover", (e) => {
        e.preventDefault();
        fileUpload.classList.add("dragover");
      });
      fileUpload.addEventListener("dragleave", () => {
        fileUpload.classList.remove("dragover");
      });
      fileUpload.addEventListener("drop", (e) => {
        e.preventDefault();
        fileUpload.classList.remove("dragover");
        const files = e.dataTransfer.files;
        handleFiles(files);
      });
      fileInput.addEventListener("change", (e) => {
        handleFiles(e.target.files);
      });

      function handleFiles(files) {
        Array.from(files).forEach(uploadFile);
      }

      async function uploadFile(file) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("user_id", userId.value);

        if (companyId.value.trim()) {
          formData.append("company_id", companyId.value);
        }

        try {
          const response = await fetch(`${API_BASE}/upload`, {
            method: "POST",
            body: formData,
          });

          if (response.ok) {
            const result = await response.json();
            addMessage(
              "assistant",
              `Document "${file.name}" uploaded successfully! Created ${result.chunks_created} chunks.`
            );
            loadDocuments();
          } else {
            const error = await response.json();
            addMessage(
              "assistant",
              `Error uploading "${file.name}": ${error.detail}`
            );
          }
        } catch (error) {
          addMessage(
            "assistant",
            `Error uploading "${file.name}": ${error.message}`
          );
        }
      }

      // Chat functionality
      sendBtn.addEventListener("click", sendMessage);
      chatInput.addEventListener("keypress", (e) => {
        if (e.key === "Enter") sendMessage();
      });

      async function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        addMessage("user", message);
        chatInput.value = "";
        sendBtn.disabled = true;
        loading.classList.add("show");

        try {
          const requestBody = {
            query: message,
            user_id: userId.value,
            session_id: sessionId,
          };

          if (companyId.value.trim()) {
            requestBody.company_id = companyId.value;
          }

          const response = await fetch(`${API_BASE}/chat/enhanced`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          });

          if (response.ok) {
            const result = await response.json();
            sessionId = result.sessionId;

            let responseText = result.response;
            if (result.reasoning) {
              responseText += "\n\nReasoning:\n" + result.reasoning;
            }

            // Add enhanced context information if available
            if (result.contextSources) {
              const sources = result.contextSources;
              const totalSources =
                (sources.documents || 0) + (sources.conversation || 0);
              responseText += `\n\n📊 Context Sources: ${totalSources} total (${
                sources.documents || 0
              } documents, ${sources.conversation || 0} conversation)`;
            }

            // Add rich document citations if available
            if (result.documentContext && result.documentContext !== null) {
              const citations = result.documentContext.citations || [];
              const totalDocs = result.documentContext.totalDocuments || 0;
              const totalChunks = result.documentContext.totalChunks || 0;

              if (citations.length > 0) {
                responseText += `\n\n📚 Document Citations: Used ${totalChunks} chunks from ${totalDocs} documents`;

                citations.slice(0, 3).forEach((citation, index) => {
                  responseText += `\n\n📄 Citation ${index + 1}:`;
                  responseText += `\n   Document: ${
                    citation.document_name || "Unknown"
                  }`;
                  responseText += `\n   Chunks: ${citation.chunks ? citation.chunks.length : 0}`;
                  
                  // Show first chunk details
                  if (citation.chunks && citation.chunks.length > 0) {
                    const firstChunk = citation.chunks[0];
                    if (firstChunk.page_number) {
                      responseText += `\n   Page: ${firstChunk.page_number}`;
                    }
                    if (firstChunk.section) {
                      responseText += `\n   Section: ${firstChunk.section}`;
                    }
                  }
                });

                if (citations.length > 3) {
                  responseText += `\n\n... and ${
                    citations.length - 3
                  } more citations`;
                }
              }
            }

            if (
              result.conversationContext &&
              result.conversationContext.length > 0
            ) {
              responseText += `\n\n💬 Referenced ${result.conversationContext.length} previous messages`;
            }

            addMessage("assistant", responseText);
          } else {
            const error = await response.json();
            addMessage("assistant", `Error: ${error.detail}`);
          }
        } catch (error) {
          addMessage("assistant", `Error: ${error.message}`);
        } finally {
          sendBtn.disabled = false;
          loading.classList.remove("show");
        }
      }

      function addMessage(type, content) {
        const messageDiv = document.createElement("div");
        messageDiv.className = `message ${type}`;

        const contentDiv = document.createElement("div");
        contentDiv.className = "message-content";
        contentDiv.textContent = content;

        const timeDiv = document.createElement("div");
        timeDiv.className = "message-time";
        timeDiv.textContent = new Date().toLocaleTimeString();

        contentDiv.appendChild(timeDiv);
        messageDiv.appendChild(contentDiv);
        chatMessages.appendChild(messageDiv);

        chatMessages.scrollTop = chatMessages.scrollHeight;
      }



      // Load documents
      async function loadDocuments() {
        try {
          const response = await fetch(`${API_BASE}/documents`);
          if (response.ok) {
            const result = await response.json();
            displayDocuments(result.documents);
          }
        } catch (error) {
          console.error("Error loading documents:", error);
        }
      }

      function displayDocuments(documents) {
        if (documents.length === 0) {
          documentsList.innerHTML =
            '<p style="color: #95a5a6; text-align: center;">No documents uploaded yet</p>';
          return;
        }

        documentsList.innerHTML = documents
          .map(
            (doc) => `
                <div class="document-item">
                    <div class="document-name">${doc.original_filename}</div>
                    <div class="document-meta">
                        ${doc.file_type.toUpperCase()} • ${formatFileSize(
              doc.file_size
            )} • ${doc.chunks_count} chunks
                    </div>
                </div>
            `
          )
          .join("");
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      }

      // Initialize
      loadDocuments();
    </script>
  </body>
</html>
