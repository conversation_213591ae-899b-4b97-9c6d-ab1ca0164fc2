import uuid
import json
import re
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import cast, String
import logging
from uuid import UUID
import random

from models import ChatSession, ChatMessage
from document_service import DocumentService
from ai_service import AIService
from embedding_service import EmbeddingService
from schemas import DocumentContext, DocumentCitation, Chunk, ChatMessageResponse

logger = logging.getLogger(__name__)


class ChatService:
    """Handles chat interactions and session management"""

    def __init__(self):
        self.document_service = DocumentService()
        self.ai_service = AIService()
        self.embedding_service = EmbeddingService()
        self.context_window_size = 10  # Number of recent messages to include

    def extract_name_from_query(self, query: str) -> Optional[str]:
        """
        Extract name from the first question

        Args:
            query: User's question

        Returns:
            Extracted name or None if not found
        """
        if not query:
            return None

        # Common patterns for name extraction
        patterns = [
            r"my name is (\w+)",  # "my name is <PERSON>"
            r"i'm (\w+)",  # "i'm <PERSON>"
            r"i am (\w+)",  # "i am <PERSON>"
            r"call me (\w+)",  # "call me <PERSON>"
            r"this is (\w+)",  # "this is John"
            r"(\w+) here",  # "John here"
            r"(\w+) speaking",  # "John speaking"
        ]

        query_lower = query.lower().strip()

        for pattern in patterns:
            match = re.search(pattern, query_lower)
            if match:
                name = match.group(1).strip()
                # Basic validation - name should be at least 2 characters and not common words
                if len(name) >= 2 and name not in [
                    "the",
                    "and",
                    "but",
                    "for",
                    "you",
                    "are",
                    "was",
                    "had",
                    "his",
                    "her",
                    "its",
                    "our",
                    "their",
                ]:
                    return name.title()  # Capitalize first letter

        # If no pattern matches, try to extract first word that looks like a name
        words = query_lower.split()
        if words:
            first_word = words[0]
            # Check if first word looks like a name (starts with capital letter or is reasonable length)
            if len(first_word) >= 2 and first_word.isalpha():
                # Check if it's not a common question word, pronoun, or article
                common_words = [
                    "what",
                    "when",
                    "where",
                    "why",
                    "how",
                    "who",
                    "which",
                    "can",
                    "could",
                    "would",
                    "should",
                    "will",
                    "do",
                    "does",
                    "did",
                    "is",
                    "are",
                    "was",
                    "were",
                    "have",
                    "has",
                    "had",
                    "i",
                    "you",
                    "he",
                    "she",
                    "it",
                    "we",
                    "they",
                    "me",
                    "him",
                    "her",
                    "us",
                    "them",
                    "the",
                    "a",
                    "an",
                    "and",
                    "or",
                    "but",
                    "in",
                    "on",
                    "at",
                    "to",
                    "for",
                    "of",
                    "with",
                    "by",
                    "from",
                    "up",
                    "down",
                    "out",
                    "off",
                    "over",
                    "under",
                    "into",
                    "onto",
                    "upon",
                    "within",
                    "without",
                    "through",
                    "throughout",
                    "during",
                    "before",
                    "after",
                    "since",
                    "until",
                    "while",
                    "whereas",
                    "although",
                    "though",
                    "unless",
                    "if",
                    "because",
                    "since",
                    "as",
                    "so",
                    "therefore",
                    "thus",
                    "hence",
                    "consequently",
                    "meanwhile",
                    "furthermore",
                    "moreover",
                    "however",
                    "nevertheless",
                    "nonetheless",
                    "still",
                    "yet",
                    "otherwise",
                    "else",
                    "also",
                    "too",
                    "either",
                    "neither",
                    "both",
                    "each",
                    "every",
                    "all",
                    "any",
                    "some",
                    "no",
                    "none",
                    "either",
                    "neither",
                    "this",
                    "that",
                    "these",
                    "those",
                    "my",
                    "your",
                    "his",
                    "her",
                    "its",
                    "our",
                    "their",
                    "mine",
                    "yours",
                    "his",
                    "hers",
                    "ours",
                    "theirs",
                ]
                if first_word not in common_words:
                    return first_word.title()

        return None

    def generate_session_name_from_query(self, query: str) -> str:
        """
        Generate a session name from the first question

        Args:
            query: User's question

        Returns:
            Generated session name
        """
        if not query:
            return "New Chat"

        # Clean the query
        query = query.strip()

        # If it's very short, use it as is
        if len(query) <= 50:
            return query

        # For longer queries, extract the first meaningful part
        # Remove common prefixes and get the core question
        prefixes_to_remove = [
            "can you",
            "could you",
            "would you",
            "please",
            "i need",
            "i want",
            "help me",
            "explain",
            "tell me",
            "show me",
            "what is",
            "how to",
            "how do",
            "why does",
            "when should",
            "where can",
            "which is",
            "who is",
            "what are",
            "how are",
        ]

        query_lower = query.lower()
        for prefix in prefixes_to_remove:
            if query_lower.startswith(prefix):
                query = query[len(prefix) :].strip()
                break

        # Take the first sentence or first 50 characters
        if "." in query:
            first_sentence = query.split(".")[0].strip()
            if len(first_sentence) > 10:  # Only use if it's substantial
                return first_sentence[:50] + ("..." if len(first_sentence) > 50 else "")

        # If no sentence break, take first 50 characters
        return query[:50] + ("..." if len(query) > 50 else "")

    def create_session(
        self,
        db: Session,
        user_id: UUID,
        company_id: Optional[UUID] = None,
        name: Optional[str] = None,
    ) -> str:
        """
        Create a new chat session

        Args:
            db: Database session
            user_id: User ID (required)
            company_id: Company ID (optional)
            name: Optional name for the session

        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        session = ChatSession(
            sessionId=session_id, userId=user_id, companyId=company_id, name=name
        )
        db.add(session)
        db.commit()
        return session_id

    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """
        Calculates the Levenshtein distance between two strings.
        This is a measure of the number of edits (insertions, deletions,
        or substitutions) needed to change one string into the other.
        """
        if len(s1) > len(s2):
            s1, s2 = s2, s1

        distances = range(len(s1) + 1)
        for i2, c2 in enumerate(s2):
            distances_ = [i2+1]
            for i1, c1 in enumerate(s1):
                if c1 == c2:
                    distances_.append(distances[i1])
                else:
                    distances_.append(1 + min((distances[i1], distances[i1 + 1], distances_[-1])))
            distances = distances_
        return distances[-1]

    def check_if_conversation_is_greeting_or_farewell(self, text: str, threshold: float = 0.8) -> tuple[bool, Optional[str]]:
        """
        Checks if a string is a greeting or a farewell using fuzzy string matching.

        This function determines if the user's input is highly similar to a known
        greeting or farewell phrase, allowing for typos and slight variations.

        It handles:
        - Typos (e.g., "helo", "goodby").
        - Leading/trailing whitespace and capitalization.
        - The optional inclusion of the bot's name "Lexi".
        - Common punctuation.

        Args:
            text: The input string to check.
            threshold: The similarity score required to be considered a match.
                       Defaults to 0.8 (80%).

        Returns:
            A tuple: (True, "greeting") if a greeting is detected.
            A tuple: (True, "farewell") if a farewell is detected.
            A tuple: (False, None) if neither is detected.
        """
        # --- Define Lists ---
        greetings_list = [
            "hello", "hi", "hey", "hi there", "hey there", "hello there", "good morning", "morning",
            "good afternoon", "afternoon", "good evening", "evening", "greetings",
            "salutations", "good day", "it is a pleasure to meet you",
            "pleased to meet you", "how are things", "how's life",
            "how have you been", "how's your day", "how's your day going", "sup",
            "yo", "what's good", "what's crackin'", "what's poppin'", "howdy",
            "hey y'all", "g'day", "how ya going", "hiya", "wotcher", "ey up",
            "nice to see you", "good to see you", "great to see you",
            "long time no see", "it's been a while", "hail", "well met",
            "good morrow", "good even", "dear", "to whom it may concern",
            "hi everyone", "hello all", "how do you do", "how are you",
            "what's up", "whats up", "what's new", "whats new",
            "what's going on", "whats going on", "what's happening", "whats happening",
            "how's it going", "hows it going", "alright", "you alright", "what's the craic",
            "hi, how are you", "hello, how are you", "hey, how are you",
            "fancy seeing you here", "look who it is", "look what the cat dragged in"
        ]
        farewells_list = [
            "goodbye", "bye", "bye-bye", "farewell", "see you later", "see ya later",
            "see you soon", "see ya soon", "talk to you later", "ttyl", "take care",
            "catch you later", "have a good one", "have a nice day", "have a great day",
            "good night", "later", "laters", "peace", "peace out", "i'm out",
            "so long", "until next time", "cheerio", "ta-ta for now", "ttfn"
        ]

        # 1. Basic input validation
        if not isinstance(text, str) or not text.strip():
            return (False, None)

        # 2. Clean the input string
        cleaned_text = text.lower()
        cleaned_text = re.sub(r'\blexi\b', '', cleaned_text)
        cleaned_text = re.sub(r'[.,!?\']', '', cleaned_text)
        cleaned_text = cleaned_text.strip()

        if not cleaned_text:
            return (False, None)

        # 3. Check against greetings list first
        for greeting in greetings_list:
            cleaned_greeting = re.sub(r'[\']', '', greeting)
            distance = self._levenshtein_distance(cleaned_text, cleaned_greeting)
            max_len = max(len(cleaned_text), len(cleaned_greeting))
            if max_len > 0:
                similarity = 1 - (distance / max_len)
                if similarity >= threshold:
                    return (True, "greeting")

        # 4. If not a greeting, check against farewells list
        for farewell in farewells_list:
            cleaned_farewell = re.sub(r'[\']', '', farewell)
            distance = self._levenshtein_distance(cleaned_text, cleaned_farewell)
            max_len = max(len(cleaned_text), len(cleaned_farewell))
            if max_len > 0:
                similarity = 1 - (distance / max_len)
                if similarity >= threshold:
                    return (True, "farewell")

        # 5. If no match is found
        return (False, None)

    def _classify_query_type(self, query: str) -> str:
        """
        Classify the type of query to determine appropriate response strategy
        """
        query_lower = query.lower()

        # Document-related keywords
        doc_keywords = [
            "document",
            "file",
            "upload",
            "compliance",
            "legal",
            "policy",
            "regulation",
            "requirement",
            "standard",
        ]
        # System-related keywords
        system_keywords = [
            "how do you work",
            "what can you do",
            "your capabilities",
            "your process",
            "explain yourself",
            "your features",
        ]
        # Chat-related keywords
        chat_keywords = [
            "hello",
            "hi",
            "how are you",
            "thanks",
            "goodbye",
            "previous",
            "earlier",
            "remember",
            "recall",
        ]

        if any(keyword in query_lower for keyword in doc_keywords):
            return "document"
        elif any(keyword in query_lower for keyword in system_keywords):
            return "system"
        elif any(keyword in query_lower for keyword in chat_keywords):
            return "chat"
        else:
            return "general"

    def _search_conversation_context(
        self, query_embedding: List[float], session_id: int, db: Session, top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant conversation context using embeddings
        """
        try:
            # Get all messages for this session with embeddings
            messages = (
                db.query(ChatMessage)
                .filter(
                    ChatMessage.sessionId == session_id,
                    ChatMessage.messageEmbedding.isnot(None),
                )
                .order_by(ChatMessage.timestamp.desc())
                .limit(50)  # Limit to recent messages for performance
                .all()
            )

            if not messages:
                return []

            # Extract embeddings
            message_embeddings = []
            valid_messages = []

            for message in messages:
                if message.messageEmbedding is not None:
                    # Convert pgvector Vector to list
                    if hasattr(message.messageEmbedding, "tolist"):
                        embedding_list = message.messageEmbedding.tolist()
                    else:
                        embedding_list = list(message.messageEmbedding)

                    message_embeddings.append(embedding_list)
                    valid_messages.append(message)

            if not message_embeddings:
                return []

            # Perform similarity search
            top_indices = self.embedding_service.similarity_search(
                query_embedding, message_embeddings, top_k
            )

            # Return relevant messages
            relevant_messages = []
            for idx in top_indices:
                message = valid_messages[idx]
                relevant_messages.append(
                    {
                        "id": message.id,
                        "content": message.content,
                        "messageType": message.messageType,
                        "timestamp": message.timestamp.isoformat(),
                        "queryType": message.queryType,
                        "confidence": message.confidence,
                    }
                )

            return relevant_messages

        except Exception as e:
            logger.error(f"Error searching conversation context: {e}")
            return []

    def _create_document_context(self, relevant_chunks: List[Chunk]) -> DocumentContext:
        """
        Create DocumentContext from relevant chunks
        
        Args:
            relevant_chunks: List of Chunk objects from search
            
        Returns:
            DocumentContext object with citations
        """
        if not relevant_chunks:
            return DocumentContext(
                citations=[],
                totalDocuments=0,
                totalChunks=0
            )
        
        # Group chunks by document for better organization
        documents_used = {}
        for chunk in relevant_chunks:
            doc_id = chunk.document_id
            doc_name = chunk.document_name
            
            if doc_id not in documents_used:
                documents_used[doc_id] = {
                    "document_id": doc_id,
                    "document_name": doc_name,
                    "chunks": []
                }
            
            # Use the Chunk object directly
            documents_used[doc_id]["chunks"].append(chunk)
        
        # Create DocumentCitation objects
        citations = []
        for doc_data in documents_used.values():
            citation = DocumentCitation(
                document_id=doc_data["document_id"],
                document_name=doc_data["document_name"],
                chunks=doc_data["chunks"]
            )
            citations.append(citation)
        
        return DocumentContext(
            citations=citations,
            totalDocuments=len(documents_used),
            totalChunks=len(relevant_chunks)
        )

    def _parse_document_context_from_db(self, document_context_json: str) -> Optional[DocumentContext]:
        """
        Parse DocumentContext from JSON string stored in database
        
        Args:
            document_context_json: JSON string from database
            
        Returns:
            DocumentContext object or None if parsing fails
        """
        if not document_context_json:
            return None
            
        try:
            data = json.loads(str(document_context_json))
            
            # Parse citations
            citations = []
            for citation_data in data.get("citations", []):
                chunks = []
                for chunk_data in citation_data.get("chunks", []):
                    chunk = Chunk(
                        chunk_id=chunk_data["chunk_id"],
                        chunk_text=chunk_data["chunk_text"],
                        chunk_index=chunk_data["chunk_index"],
                        document_id=chunk_data["document_id"],
                        document_name=chunk_data["document_name"],
                        page_number=chunk_data.get("page_number"),
                        section=chunk_data.get("section")
                    )
                    chunks.append(chunk)
                
                citation = DocumentCitation(
                    document_id=citation_data["document_id"],
                    document_name=citation_data["document_name"],
                    chunks=chunks
                )
                citations.append(citation)
            
            return DocumentContext(
                citations=citations,
                totalDocuments=data.get("totalDocuments", 0),
                totalChunks=data.get("totalChunks", 0)
            )
        except (json.JSONDecodeError, TypeError, KeyError) as e:
            logger.error(f"Error parsing document context: {e}")
            return None

    def process_query(
        self,
        query: str,
        session_id: Optional[str],
        db: Session,
        user_id: UUID,
        company_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Process user query and generate response

        Args:
            query: User's question
            session_id: Chat session ID
            db: Database session
            user_id: User ID (required)
            company_id: Company ID (optional)

        Returns:
            Dictionary with response and metadata
        """
        try:
            # Get or create session
            session = None
            if session_id:
                session = (
                    db.query(ChatSession)
                    .filter(ChatSession.sessionId == session_id)
                    .first()
                )
            if not session:
                # Generate session name from first question if this is a new session
                session_name = self.generate_session_name_from_query(query)
                session_id = self.create_session(
                    db, user_id, company_id, name=session_name
                )
                session = (
                    db.query(ChatSession)
                    .filter(ChatSession.sessionId == session_id)
                    .first()
                )
            else:
                # If session exists but doesn't have a name, generate it from the first message
                session_name_attr = getattr(session, "name", None)
                if not session_name_attr or session_name_attr == "New Chat":
                    session_name = self.generate_session_name_from_query(query)
                    setattr(session, "name", session_name)
                    db.commit()

            # Ensure session exists
            if not session:
                raise Exception("Failed to create or retrieve session")

            # Classify query type
            query_type = self._classify_query_type(query)

            # Generate embedding for user query
            query_embedding = self.embedding_service.generate_embedding(query)

            # Save user message with embedding
            user_message = ChatMessage(
                sessionId=session.id,
                messageType="user",
                content=query,
                messageEmbedding=query_embedding,
                queryType=query_type,
            )
            db.add(user_message)
            db.commit()
            db.refresh(user_message)

            if company_id:
                has_embeddings = self.document_service.has_completed_embeddings(
                    user_id, company_id, db
                )
                if not has_embeddings:

                    bot_message = ChatMessage(
                        sessionId=session.id,
                        messageType="assistant",
                        content="No documents found for this company. Please upload and process documents first. And wait for the documents to be processed before asking questions.",
                        documentContext=json.dumps({
                            "citations": [],
                            "totalDocuments": 0,
                            "totalChunks": 0
                        })
                    )
                    db.add(bot_message)
                    db.commit()
                    return {
                        "sessionId": session_id,
                        "response": "No documents found for this company. Please upload and process documents first. And wait for the documents to be processed before asking questions.",
                        "reasoning": "",
                        "contextChunks": [],
                        "chunksUsed": 0,
                        "confidence": "high",
                        "documentContext": {
                            "citations": [],
                            "totalDocuments": 0,
                            "totalChunks": 0
                        },
                    }
                else:
                    has_embeddings = self.document_service.has_completed_embeddings(
                        user_id, None, db
                    )
                    if not has_embeddings:
                        empty_context = DocumentContext(
                            citations=[],
                            totalDocuments=0,
                            totalChunks=0
                        )
                        bot_message = ChatMessage(
                            sessionId=session.id,
                            messageType="assistant",
                            content="No documents found for this user. Please upload and process documents first. And wait for the documents to be processed before asking questions.",
                            documentContext=json.dumps(empty_context.model_dump())
                        )
                        db.add(bot_message)
                        db.commit()
                        return {
                            "sessionId": session_id,
                            "response": "No documents found for this user. Please upload and process documents first. And wait for the documents to be processed before asking questions.",
                            "reasoning": "",
                            "contextChunks": [],
                            "chunksUsed": 0,
                            "confidence": "high",
                            "documentContext": empty_context,
                        }

            # Check if this is a pure greeting - if so, skip context search
            is_greeting_or_farewell, type_of_greeting = self.check_if_conversation_is_greeting_or_farewell(text=query)
            
            if is_greeting_or_farewell and type_of_greeting:
                # For pure greetings, provide a simple response without context search
                greeting_response = self.get_random_greeting_response(type_of_greeting)
                
                # Save assistant message for pure greeting
                assistant_message = ChatMessage(
                    sessionId=session.id,
                    messageType="assistant",
                    content=greeting_response,
                    messageEmbedding=self.embedding_service.generate_embedding(greeting_response),
                    queryType=query_type,
                    confidence="high",
                    reasoning="Pure greeting detected - no context search needed",
                    documentContext= None
                )
                db.add(assistant_message)
                db.commit()
                
                return {
                    "sessionId": session_id,
                    "response": greeting_response,
                    "reasoning": "Pure greeting detected - no context search needed",
                    "contextChunks": [],
                    "conversationContext": [],
                    "chunksUsed": 0,
                    "confidence": "high",
                    "contextSources": {"documents": 0, "conversation": 0},
                    "documentContext": None,
                }
            
            # Search for relevant conversation context
            session_id_int = getattr(session, "id", 0)
            conversation_context = self._search_conversation_context(
                query_embedding, session_id_int, db
            )

            # Search for relevant document chunks filtered by user_id and company_id
            documents_info, relevant_chunks = self.document_service.search_documents(
                query, db, user_id, company_id
            )

            # Get recent chat history
            chat_history = self.get_chat_history(session_id, db)
        
            # Generate AI response with context awareness
            ai_response = self.ai_service.generate_response_with_context(
                query, relevant_chunks, conversation_context, chat_history, documents_info
            )
            # Normalize AI response in case the AI layer returned a list or malformed structure
            if isinstance(ai_response, list):
                ai_response = ai_response[0] if ai_response else {}

            response_text = ai_response.get("response", "")
            reasoning = ai_response.get("reasoning", "")
            citations = ai_response.get("citations", {"document_used_chunks": []})

            # Normalize citations if AI returned a bare list
            if isinstance(citations, list):
                normalized_citations = {"document_used_chunks": []}
                for item in citations:
                    if isinstance(item, int):
                        normalized_citations["document_used_chunks"].append(item)
                    elif isinstance(item, str) and item.isdigit():
                        normalized_citations["document_used_chunks"].append(int(item))
                citations = normalized_citations

            print("-------------------------------- AI Response --------------------------------")
            print(ai_response)
            print("-------------------------------- AI Response --------------------------------")
            print(response_text)
            print("-------------------------------- AI Response --------------------------------")
            print(citations)
            print("-------------------------------- AI Response --------------------------------")
           




            # Generate embedding for assistant response
            response_embedding = self.embedding_service.generate_embedding(
                response_text
            )

            # Prepare document context with rich citation information
            # Use cited chunks if available, otherwise use all relevant chunks
            cited_chunk_ids = citations.get("document_used_chunks", []) if isinstance(citations, dict) else []
            cited_chunks = [chunk for chunk in relevant_chunks if chunk.chunk_id in cited_chunk_ids]
            document_context = self._create_document_context(cited_chunks)
           
            # Save assistant message with new context fields
            assistant_message = ChatMessage(
                sessionId=session.id,
                messageType="assistant",
                content=response_text,
                messageEmbedding=response_embedding,
                queryType=query_type,
                confidence=ai_response.get("confidence", "high"),
                reasoning=reasoning,
                documentContext=json.dumps(document_context.model_dump())
            )
            db.add(assistant_message)
            db.commit()

            return {
                "sessionId": session_id,
                "response": response_text,
                "reasoning": reasoning,
                "citations": citations,
                "contextChunks": cited_chunks,  # Keep for backward compatibility
                "conversationContext": conversation_context,
                "chunksUsed": len(cited_chunks),
                "confidence": ai_response.get("confidence", "high"),
                "contextSources": {
                    "documents": len(cited_chunks),
                    "conversation": len(conversation_context),
                },
                "documentContext": document_context,  # Now using DocumentContext object
            }

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            empty_context = DocumentContext(
                citations=[],
                totalDocuments=0,
                totalChunks=0
            )
            return {
                "sessionId": session_id,
                "response": f"I apologize, but I encountered an error: {str(e)}",
                "reasoning": None,
                "citations": {"document_used_chunks": []},
                "contextChunks": [],
                "conversationContext": [],
                "chunksUsed": 0,
                "confidence": "error",
                "contextSources": {"documents": 0, "conversation": 0},
                "documentContext": empty_context,
            }

    def get_random_greeting_response(self, greeting_type: str) -> str:
        """
        Returns a random greeting or farewell response based on the type.
        
        Args:
            greeting_type: Either "greeting" or "farewell"
            
        Returns:
            A randomly selected response string
        """
        if greeting_type == "greeting":
            greetings = [
                "Hello! I'm here to help you with your compliance and document analysis questions. How can I assist you today?",
                "Hi there! I'm ready to help you with any compliance or document analysis questions you might have. What can I do for you?",
                "Hey! Welcome to the compliance assistant. I'm here to help you with document analysis and compliance questions. How can I be of service?",
                "Good day! I'm your compliance and document analysis assistant. What would you like to work on today?",
                "Greetings! I'm here to help you navigate compliance and document analysis. What can I assist you with?",
                "Hi! I'm your AI assistant for compliance and document analysis. How can I help you today?",
                "Hello there! I'm ready to help you with compliance questions and document analysis. What's on your mind?",
                "Hey there! I'm your compliance assistant. How can I help you with your documents and analysis today?",
                "Good to see you! I'm here to help with compliance and document analysis. What would you like to work on?",
                "Hi! I'm your AI assistant for compliance and document analysis. How can I be of help today?"
            ]
            return random.choice(greetings)
        elif greeting_type == "farewell":
            farewells = [
                "Goodbye! Feel free to come back if you have more compliance or document analysis questions.",
                "See you later! I'm here whenever you need help with compliance or document analysis.",
                "Take care! Don't hesitate to return if you have more questions about compliance or documents.",
                "Bye for now! I'll be here when you need assistance with compliance and document analysis.",
                "Have a great day! I'm always available to help with your compliance and analysis needs.",
                "Until next time! Feel free to reach out if you need help with compliance or documents.",
                "Goodbye! I'm here whenever you need assistance with compliance and document analysis.",
                "See you soon! Don't forget I'm here to help with compliance and analysis questions.",
                "Take care! I'm always ready to assist with compliance and document analysis.",
                "Bye! I'll be here when you need help with compliance or document analysis."
            ]
            return random.choice(farewells)
        else:
            # Fallback response
            return "Hello! I'm here to help you with your compliance and document analysis questions. How can I assist you today?"

    def get_chat_history(self, session_id: str, db: Session) -> List[ChatMessageResponse]:
        """
        Get chat history for a session

        Args:
            session_id: Chat session ID
            db: Database session

        Returns:
            List of ChatMessageResponse objects
        """
        try:
            session = (
                db.query(ChatSession)
                .filter(ChatSession.sessionId == session_id)
                .first()
            )
            if not session:
                return []

            messages = (
                db.query(ChatMessage)
                .filter(ChatMessage.sessionId == session.id)
                .order_by(ChatMessage.timestamp)
                .all()
            )

            result = []
            for msg in messages:
                # Parse document context using proper schema
                document_context = self._parse_document_context_from_db(msg.documentContext)

                result.append(
                    ChatMessageResponse(
                        id=msg.id,
                        type=msg.messageType,
                        content=msg.content,
                        timestamp=msg.timestamp.isoformat(),
                        documentContext=document_context,
                        queryType=msg.queryType,
                        confidence=msg.confidence,
                        reasoning=msg.reasoning,
                    )
                )
            return result

        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []

    def get_session_info(
        self, session_id: str, db: Session
    ) -> Optional[Dict[str, Any]]:
        """
        Get session information

        Args:
            session_id: Chat session ID
            db: Database session

        Returns:
            Session information dictionary
        """
        try:
            session = (
                db.query(ChatSession)
                .filter(ChatSession.sessionId == session_id)
                .first()
            )
            if not session:
                return None

            message_count = (
                db.query(ChatMessage)
                .filter(ChatMessage.sessionId == session.id)
                .count()
            )

            return {
                "sessionId": session.sessionId,
                "userId": str(session.userId),
                "companyId": (
                    str(session.companyId)
                    if getattr(session, "companyId", None)
                    else None
                ),
                "name": session.name,
                "createdAt": session.createdAt.isoformat(),
                "messageCount": message_count,
            }

        except Exception as e:
            logger.error(f"Error getting session info: {e}")
            return None

    def list_all_sessions(
        self,
        db: Session,
        user_id: Optional[UUID] = None,
        company_id: Optional[UUID] = None,
    ) -> List[Dict[str, Any]]:
        """
        List chat sessions with metadata, optionally filtered by user_id and/or company_id

        Args:
            db: Database session
            user_id: Optional user ID to filter sessions
            company_id: Optional company ID to filter sessions

        Returns:
            List of session information dictionaries
        """
        try:
            query = db.query(ChatSession)

            # Apply filters if provided
            if user_id is not None:
                query = query.filter(cast(ChatSession.userId, String) == str(user_id))
            if company_id is not None:
                query = query.filter(
                    cast(ChatSession.companyId, String) == str(company_id)
                )

            sessions = query.order_by(ChatSession.createdAt.desc()).all()

            result = []
            for session in sessions:
                # Get message count for this session
                message_count = (
                    db.query(ChatMessage)
                    .filter(ChatMessage.sessionId == session.id)
                    .count()
                )

                # Get the first user message to show as preview
                first_message = (
                    db.query(ChatMessage)
                    .filter(
                        ChatMessage.sessionId == session.id,
                        ChatMessage.messageType == "user",
                    )
                    .order_by(ChatMessage.timestamp)
                    .first()
                )

                # Get the last message timestamp
                last_message = (
                    db.query(ChatMessage)
                    .filter(ChatMessage.sessionId == session.id)
                    .order_by(ChatMessage.timestamp.desc())
                    .first()
                )

                result.append(
                    {
                        "sessionId": session.sessionId,
                        "userId": str(session.userId),
                        "companyId": (
                            str(session.companyId)
                            if getattr(session, "companyId", None)
                            else None
                        ),
                        "name": session.name or f"Session {session.id}",
                        "createdAt": session.createdAt.isoformat(),
                        "lastActivity": (
                            last_message.timestamp.isoformat()
                            if last_message
                            else session.createdAt.isoformat()
                        ),
                        "messageCount": message_count,
                        "preview": (
                            first_message.content[:100] + "..."
                            if first_message and len(str(first_message.content)) > 100
                            else (
                                first_message.content
                                if first_message
                                else "No messages yet"
                            )
                        ),
                    }
                )

            return result

        except Exception as e:
            logger.error(f"Error listing sessions: {e}")
            return []

    def list_sessions_by_user(self, db: Session, user_id: UUID) -> List[Dict[str, Any]]:
        """
        List chat sessions for a specific user

        Args:
            db: Database session
            user_id: User ID to filter sessions

        Returns:
            List of session information dictionaries
        """
        return self.list_all_sessions(db, user_id=user_id)

    def list_sessions_by_company(
        self, db: Session, company_id: UUID
    ) -> List[Dict[str, Any]]:
        """
        List chat sessions for a specific company

        Args:
            db: Database session
            company_id: Company ID to filter sessions

        Returns:
            List of session information dictionaries
        """
        return self.list_all_sessions(db, company_id=company_id)
