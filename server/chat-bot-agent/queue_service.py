import queue
import threading
import time
import logging
import psutil
import os
import re
from typing import Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from database import get_db
from models import Document, DocumentChunk
from document_processor import DocumentProcessor
from text_chunker import <PERSON>Chunker
from embedding_service import EmbeddingService
from config import config

logger = logging.getLogger(__name__)

def sanitize_text_for_database(text: str) -> str:
    """Remove NUL characters and other problematic characters from text before database insertion"""
    if not text:
        return ""
    
    # Remove NUL characters (0x00) - these cause PostgreSQL errors
    text = text.replace('\x00', '')
    
    # Remove other control characters that might cause issues
    # Keep newlines, tabs, and carriage returns but remove others
    text = ''.join(char for char in text if char >= ' ' or char in '\n\t\r')
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()

class DocumentProcessingTask:
    """Represents a document processing task"""
    
    def __init__(self, task_id: str, file_content: bytes, original_filename: str, 
                 user_id: UUID, company_id: Optional[UUID], document_id: int):
        self.task_id: str = task_id
        self.file_content: bytes = file_content
        self.original_filename: str = original_filename
        self.user_id: UUID = user_id
        self.company_id: Optional[UUID] = company_id
        self.document_id: int = document_id
        self.status: str = "queued"  # queued, processing, completed, failed
        self.progress: int = 0  # 0-100
        self.error_message: Optional[str] = None
        self.chunks_created: int = 0
        self.created_at: float = time.time()
        self.started_at: Optional[float] = None
        self.completed_at: Optional[float] = None

class DocumentQueueService:
    """Manages document processing queue with resource constraints"""
    
    def __init__(self):
        self.task_queue = queue.Queue()
        self.tasks: Dict[str, DocumentProcessingTask] = {}
        self.processing_thread = None
        self.shutdown_event = threading.Event()
        self.lock = threading.Lock()
        
        # Processing control - ensure only one document is processed at a time
        self.is_processing = False
        self.processing_lock = threading.Lock()
        
        # Resource management
        self.max_cpu_percent = 95  # Use max 50% CPU
        self.max_memory_percent = 95  # Use max 50% memory
        
        # Initialize processing components
        self.document_processor = DocumentProcessor(config.UPLOAD_DIR)
        self.text_chunker = TextChunker()
        self.embedding_service = EmbeddingService()
        
        # Start processing thread
        self._start_processing_thread()
    
    def _start_processing_thread(self):
        """Start the background processing thread"""
        self.processing_thread = threading.Thread(
            target=self._process_queue,
            daemon=True,
            name="DocumentProcessingThread"
        )
        self.processing_thread.start()
        logger.info("Document processing queue thread started")
    
    def _check_resource_usage(self) -> bool:
        """Check if system resources are within limits"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > self.max_cpu_percent:
                logger.debug(f"CPU usage too high: {cpu_percent}% > {self.max_cpu_percent}%")
                return False
            
            if memory_percent > self.max_memory_percent:
                logger.debug(f"Memory usage too high: {memory_percent}% > {self.max_memory_percent}%")
                return False
            
            return True
        except Exception as e:
            logger.warning(f"Error checking resource usage: {e}")
            return True  # Continue processing if we can't check resources
    
    def _process_queue(self):
        """Main processing loop for the queue - ensures only one document is processed at a time"""
        while not self.shutdown_event.is_set():
            try:
                # Wait for a task with timeout
                try:
                    task = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Check if we're already processing a document
                with self.processing_lock:
                    if self.is_processing:
                        # Put task back in queue and wait
                        self.task_queue.put(task)
                        logger.debug("Another document is being processed, waiting...")
                        time.sleep(2)  # Wait 2 seconds before checking again
                        continue
                    
                    # Mark that we're starting to process
                    self.is_processing = True
                
                try:
                    # Check resource usage before processing
                    if not self._check_resource_usage():
                        # Put task back in queue and wait
                        self.task_queue.put(task)
                        time.sleep(5)  # Wait 5 seconds before checking again
                        continue
                    
                    # Process the task
                    self._process_task(task)
                    
                finally:
                    # Always mark processing as complete
                    with self.processing_lock:
                        self.is_processing = False
                
            except Exception as e:
                logger.error(f"Error in processing queue: {e}")
                # Ensure processing flag is reset on error
                with self.processing_lock:
                    self.is_processing = False
                time.sleep(1)
    
    def _process_task(self, task: DocumentProcessingTask):
        """Process a single document task"""
        try:
            with self.lock:
                task.status = "processing"
                task.started_at = time.time()
                task.progress = 10
            
            logger.info(f"Starting processing for task {task.task_id}: {task.original_filename}")
            
            try:
                # Update progress
                with self.lock:
                    task.progress = 20
                
                # Extract text from document
                file_extension = os.path.splitext(task.original_filename)[1].lower()
                unique_filename = f"{task.task_id}{file_extension}"
                file_path = self.document_processor.save_file(task.file_content, unique_filename)
                
                with self.lock:
                    task.progress = 30
                
                # Extract text with page and section information
                pages_data = self.document_processor.extract_text(file_path, file_extension)
                
                with self.lock:
                    task.progress = 50
                
                # Create chunks
                chunks_data = self.text_chunker.create_chunks(pages_data, task.document_id)
                
                with self.lock:
                    task.progress = 70
                
                # Generate embeddings for chunks (this is the most resource-intensive part)
                chunks_with_embeddings = self.embedding_service.generate_chunk_embeddings(chunks_data)
                
                with self.lock:
                    task.progress = 90
                
                # Save chunks to database using a new session
                db = None
                try:
                    from database import SessionLocal
                    db = SessionLocal()
                    
                    # Update document record with file path and metadata
                    from models import Document
                    document = db.query(Document).filter(Document.id == task.document_id).first()
                    if document:
                        # Calculate metadata
                        total_pages = len(pages_data)
                        total_chunks = len(chunks_with_embeddings)
                        
                        # Use SQLAlchemy update to avoid attribute assignment issues
                        db.query(Document).filter(Document.id == task.document_id).update({
                            Document.filePath: file_path,
                            Document.totalPages: total_pages,
                            Document.totalChunks: total_chunks,
                            Document.isVectorEmbeddingComplete: True
                        })
                        db.commit()
                    
                    # Save chunks to database
                    for chunk_data in chunks_with_embeddings:
                        # Sanitize text before database insertion
                        sanitized_text = sanitize_text_for_database(chunk_data['chunk_text'])
                        sanitized_section = sanitize_text_for_database(chunk_data['section'])
                        
                        chunk = DocumentChunk(
                            documentId=chunk_data['document_id'],
                            chunkText=sanitized_text,
                            chunkIndex=chunk_data['chunk_index'],
                            pageNumber=chunk_data['page_number'],
                            section=sanitized_section,
                            embedding=chunk_data['embedding']
                        )
                        db.add(chunk)
                    
                    db.commit()
                    db.close()
                    
                    # Update task status
                    with self.lock:
                        task.status = "completed"
                        task.progress = 100
                        task.chunks_created = len(chunks_with_embeddings)
                        task.completed_at = time.time()
                    
                    logger.info(f"Completed processing for task {task.task_id}: {task.chunks_created} chunks created")
                    
                except Exception as db_error:
                    error_msg = str(db_error)
                    logger.error(f"Database error processing task {task.task_id}: {error_msg}")
                    
                    # Check if it's a NUL character error and provide specific guidance
                    if "NUL" in error_msg or "0x00" in error_msg:
                        logger.error(f"NUL character detected in task {task.task_id}. This should be handled by text sanitization.")
                        logger.error("Please check the document content for binary data or corrupted text.")
                    
                    if db is not None:
                        db.rollback()
                        db.close()
                    raise db_error
                
            except Exception as e:
                logger.error(f"Error processing task {task.task_id}: {e}")
                with self.lock:
                    task.status = "failed"
                    task.error_message = str(e)
                    task.completed_at = time.time()
                
        except Exception as e:
            logger.error(f"Unexpected error in task processing: {e}")
            with self.lock:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = time.time()
    
    def add_task(self, file_content: bytes, original_filename: str, 
                 user_id: UUID, company_id: Optional[UUID], document_id: int) -> str:
        """Add a document processing task to the queue"""
        task_id = f"task_{int(time.time() * 1000)}_{document_id}"
        
        task = DocumentProcessingTask(
            task_id=task_id,
            file_content=file_content,
            original_filename=original_filename,
            user_id=user_id,
            company_id=company_id,
            document_id=document_id
        )
        
        with self.lock:
            self.tasks[task_id] = task
        
        queue_position = self.task_queue.qsize() + 1
        self.task_queue.put(task)
        
        if queue_position > 1:
            logger.info(f"Added task {task_id} to queue position {queue_position} for document {original_filename} (will be processed after current document completes)")
        else:
            logger.info(f"Added task {task_id} to queue for document {original_filename} (will start processing immediately)")
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a processing task"""
        with self.lock:
            task = self.tasks.get(task_id)
        
        if not task:
            return None
        
        return {
            'taskId': task.task_id,
            'status': task.status,
            'progress': task.progress,
            'errorMessage': task.error_message,
            'chunksCreated': task.chunks_created,
            'createdAt': task.created_at,
            'startedAt': task.started_at,
            'completedAt': task.completed_at,
            'originalFilename': task.original_filename
        }
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get overall queue status"""
        with self.lock:
            queued_count = sum(1 for task in self.tasks.values() if task.status == "queued")
            processing_count = sum(1 for task in self.tasks.values() if task.status == "processing")
            completed_count = sum(1 for task in self.tasks.values() if task.status == "completed")
            failed_count = sum(1 for task in self.tasks.values() if task.status == "failed")
            total_count = len(self.tasks)
        
        with self.processing_lock:
            is_currently_processing = self.is_processing
        
        return {
            'queueSize': self.task_queue.qsize(),
            'queuedTasks': queued_count,
            'processingTasks': processing_count,
            'completedTasks': completed_count,
            'failedTasks': failed_count,
            'totalTasks': total_count,
            'is_processing': is_currently_processing,
            'max_concurrent_processing': 1  # Only one document processed at a time
        }
    
    def is_processing_document(self) -> bool:
        """Check if a document is currently being processed"""
        with self.processing_lock:
            return self.is_processing
    
    def shutdown(self):
        """Shutdown the queue service"""
        logger.info("Shutting down document queue service...")
        self.shutdown_event.set()
        
        # Wait for any current processing to complete
        with self.processing_lock:
            if self.is_processing:
                logger.info("Waiting for current document processing to complete...")
        
        if self.processing_thread:
            self.processing_thread.join(timeout=10)
        logger.info("Document queue service shutdown complete")

    def get_queue_position(self, task_id: str) -> Optional[int]:
        """Get the position of a task in the queue (1-based, None if not found or already processing/completed)"""
        with self.lock:
            task = self.tasks.get(task_id)
        
        if not task or task.status != "queued":
            return None
        
        # Count queued tasks that were created before this one
        position = 1
        for other_task in self.tasks.values():
            if (other_task.status == "queued" and 
                other_task.created_at < task.created_at):
                position += 1
        
        return position

# Global instance
document_queue_service = DocumentQueueService() 