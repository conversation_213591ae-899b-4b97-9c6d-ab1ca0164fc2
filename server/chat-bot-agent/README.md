# LE Compliance ChatBot - Backend API Documentation

## Overview

The LE Compliance ChatBot provides a comprehensive REST API for document processing, AI-powered chat interactions, and session management. The API is built with FastAPI and supports document upload, processing, chat queries, and conversation context awareness.

**Base URL**: `http://localhost:8080`  
**API Version**: 1.0.0

---

## Table of Contents

1. [Document Upload Endpoints](#document-upload-endpoints)
2. [Chat Endpoints](#chat-endpoints)
3. [Document Management Endpoints](#document-management-endpoints)
4. [Session Management Endpoints](#session-management-endpoints)
5. [Queue Management Endpoints](#queue-management-endpoints)
6. [System Endpoints](#system-endpoints)

---

## Document Upload Endpoints

### 1. Upload Single Document

**Endpoint**: `POST /upload`

**Description**: Upload and queue a single document for processing. The document is validated, stored, and added to a processing queue for text extraction, chunking, and vector embedding generation.

**Functionality**:

- Validates file size (max 30MB)
- Validates file type (PDF, DOCX, PPTX, XLSX, XLS, DOC, PPT)
- Creates document record in database
- Queues document for processing
- Returns task ID for status tracking

**Request**:

```http
POST /upload
Content-Type: multipart/form-data

file: [document file]
user_id: [UUID] (required)
company_id: [UUID] (optional)
document_type: [string] (optional, default: "Standard")
```

**Request Parameters**:

- `file` (required): Document file to upload
- `user_id` (required): UUID of the user uploading the document
- `company_id` (optional): UUID of the company (for company-specific documents)
- `document_type` (optional): Type of document ("Standard" or "Compliance")

**Response**:

```json
{
  "documentId": 1,
  "taskId": "task_1234567890_1",
  "filename": "compliance_policy.pdf",
  "fileSize": 2048576,
  "status": "queued",
  "message": "Document uploaded and queued for processing"
}
```

**Response Fields**:

- `documentId`: Unique identifier for the document
- `taskId`: Task ID for tracking processing status
- `filename`: Generated filename for storage
- `fileSize`: Size of uploaded file in bytes
- `status`: Current processing status
- `message`: Status message

**Error Responses**:

- `400 Bad Request`: Invalid file type or missing required fields
- `413 Request Entity Too Large`: File size exceeds limit
- `500 Internal Server Error`: Processing error

---

### 2. Upload Multiple Documents

**Endpoint**: `POST /upload/multiple`

**Description**: Upload and queue multiple documents for processing. Documents are processed sequentially to ensure efficient vector embedding generation.

**Functionality**:

- Validates multiple files (max 10 files per request)
- Processes each file individually
- Queues all documents for sequential processing
- Returns detailed results for each document

**Request**:

```http
POST /upload/multiple
Content-Type: multipart/form-data

files: [document file 1, document file 2, ...]
user_id: [UUID] (required)
company_id: [UUID] (optional)
document_type: [string] (optional, default: "Standard")
```

**Request Parameters**:

- `files` (required): Array of document files (max 10)
- `user_id` (required): UUID of the user uploading documents
- `company_id` (optional): UUID of the company
- `document_type` (optional): Type of documents

**Response**:

```json
{
  "uploads": [
    {
      "documentId": 1,
      "taskId": "task_1234567890_1",
      "status": "queued",
      "message": "Document uploaded and queued for processing",
      "id": 1,
      "filename": "compliance_policy.pdf",
      "originalFilename": "compliance_policy.pdf",
      "fileSize": 2048576,
      "fileType": "application/pdf",
      "uploadedAt": "2024-01-01T10:00:00Z",
      "totalPages": null,
      "totalChunks": null,
      "chunksCount": 0,
      "documentType": "Standard"
    }
  ],
  "totalCount": 1,
  "message": "Successfully queued 1 documents for processing"
}
```

**Response Fields**:

- `uploads`: Array of upload results for each document
- `totalCount`: Total number of documents processed
- `message`: Summary message

---

## Chat Endpoints

### 3. Standard Chat Query

**Endpoint**: `POST /chat`

**Description**: Send a chat query and receive an AI-generated response based on uploaded documents and conversation context.

**Functionality**:

- Processes user query using AI (Google Gemini)
- Searches for relevant document chunks using vector similarity
- Generates contextual response based on document content
- Maintains conversation session
- Supports agentic reasoning mode
- Automatic session name generation from first query

**Request**:

```json
{
  "query": "What are the compliance requirements for data protection?",
  "userId": "123e4567-e89b-12d3-a456-************",
  "companyId": "987fcdeb-51a2-43d1-b789-123456789abc",
  "sessionId": "session-uuid"
}
```

**Request Fields**:

- `query` (required): User's question (1-1000 characters)
- `userId` (required): UUID of the user
- `companyId` (optional): UUID of the company
- `sessionId` (optional): Session identifier (defaults to "default")


**Response**:

```json
{
  "sessionId": "session-uuid",
  "response": "Based on the uploaded documents, the compliance requirements for data protection include...",
  "reasoning": null,
  "contextChunks": [
    {
      "chunk_id": 1,
      "chunk_text": "Data protection requirements...",
      "chunk_index": 5,
      "document_id": 1,
      "document_name": "compliance_policy.pdf",
      "page_number": 5,
      "section": "Compliance Guidelines"
    }
  ],
  "chunksUsed": 3,
  "confidence": "high"
}
```

**Response Fields**:

- `sessionId`: Session identifier
- `response`: AI-generated response
- `reasoning`: Step-by-step reasoning
- `contextChunks`: Relevant document chunks used
- `chunksUsed`: Number of chunks referenced
- `confidence`: Confidence level of response

---

### 4. Enhanced Chat Query

**Endpoint**: `POST /chat/enhanced`

**Description**: Enhanced chat endpoint with full conversation context awareness, including previous conversation references, self-awareness features, and document citation tracking.

**Functionality**:

- All features of standard chat
- Conversation context awareness via embeddings
- Previous message references
- Self-awareness and confidence assessment
- Context source tracking (documents vs conversation)
- Document citation with chunk details
- Multi-language support (automatic language detection)

**Request**: Same as standard chat

**Response**:

```json
{
  "sessionId": "session-uuid",
  "response": "Building on our previous conversation about data privacy...",
  "reasoning": "Based on 3 relevant document sections | Referenced 2 relevant previous exchanges",
  "contextChunks": [
    {
      "chunk_id": 1,
      "chunk_text": "Data protection requirements...",
      "chunk_index": 5,
      "document_id": 1,
      "document_name": "compliance_policy.pdf",
      "page_number": 5,
      "section": "Compliance Guidelines"
    }
  ],
  "conversationContext": [
    {
      "id": 123,
      "content": "Previous question about compliance",
      "messageType": "user",
      "timestamp": "2024-01-01T10:00:00Z",
      "queryType": "document",
      "confidence": "high"
    }
  ],
  "chunksUsed": 3,
  "confidence": "high",
  "contextSources": {
    "documents": 3,
    "conversation": 2
  },
  "documentContext": {
    "citations": [
      {
        "document_id": 1,
        "document_name": "compliance_policy.pdf",
        "chunks": [
          {
            "chunk_id": 1,
            "chunk_text": "Data protection requirements...",
            "chunk_index": 5,
            "document_id": 1,
            "document_name": "compliance_policy.pdf",
            "page_number": 5,
            "section": "Compliance Guidelines"
          }
        ]
      }
    ],
    "totalDocuments": 1,
    "totalChunks": 3
  }
}
```

**Additional Response Fields**:

- `conversationContext`: Relevant previous conversation messages
- `contextSources`: Count of sources used (documents vs conversation)
- `documentContext`: Detailed document citations with chunk information

---

### 5. Get Chat History

**Endpoint**: `GET /chat/history/{session_id}`

**Description**: Retrieve the complete chat history for a specific session with enhanced metadata.

**Functionality**:

- Retrieves all messages in a session
- Includes metadata for each message
- Shows document context and citations
- Supports conversation context features

**Request**:

```http
GET /chat/history/session-uuid
```

**Response**:

```json
{
  "sessionId": "session-uuid",
  "messages": [
    {
      "id": 1,
      "type": "user",
      "content": "What are the compliance requirements?",
      "timestamp": "2024-01-01T10:00:00Z",
      "documentContext": {
        "citations": [
          {
            "document_id": 1,
            "document_name": "compliance_policy.pdf",
            "chunks": [
              {
                "chunk_id": 1,
                "chunk_text": "Data protection requirements...",
                "chunk_index": 5,
                "document_id": 1,
                "document_name": "compliance_policy.pdf",
                "page_number": 5,
                "section": "Compliance Guidelines"
              }
            ]
          }
        ],
        "totalDocuments": 1,
        "totalChunks": 3
      },
      "queryType": "document",
      "confidence": "high",
      "reasoning": null
    },
    {
      "id": 2,
      "type": "assistant",
      "content": "Based on the documents...",
      "timestamp": "2024-01-01T10:00:01Z",
      "documentContext": {
        "citations": [
          {
            "document_id": 1,
            "document_name": "compliance_policy.pdf",
            "chunks": [
              {
                "chunk_id": 1,
                "chunk_text": "Data protection requirements...",
                "chunk_index": 5,
                "document_id": 1,
                "document_name": "compliance_policy.pdf",
                "page_number": 5,
                "section": "Compliance Guidelines"
              }
            ]
          }
        ],
        "totalDocuments": 1,
        "totalChunks": 3
      },
      "queryType": null,
      "confidence": "high",
      "reasoning": "Analyzed 3 document chunks..."
    }
  ]
}
```

---

## Document Management Endpoints

### 6. List Documents

**Endpoint**: `GET /documents`

**Description**: List all documents with optional filtering by user and company.

**Functionality**:

- Lists all documents (admin mode) or user-specific documents
- Filters by user_id and/or company_id
- Includes processing status and metadata
- Shows chunk counts and embedding status

**Request**:

```http
GET /documents?user_id=123e4567-e89b-12d3-a456-************&company_id=987fcdeb-51a2-43d1-b789-123456789abc
```

**Query Parameters**:

- `user_id` (optional): Filter by user ID
- `company_id` (optional): Filter by company ID

**Response**:

```json
{
  "documents": [
    {
      "id": 1,
      "filename": "compliance_policy.pdf",
      "originalFilename": "compliance_policy.pdf",
      "fileSize": 2048576,
      "fileType": "application/pdf",
      "uploadedAt": "2024-01-01T10:00:00Z",
      "totalPages": 15,
      "totalChunks": 45,
      "chunksCount": 45,
      "documentType": "Standard",
      "isVectorEmbeddingComplete": true
    }
  ],
  "totalCount": 1
}
```

**Response Fields**:

- `documents`: Array of document information
- `totalCount`: Total number of documents

---

### 7. Get Single Document

**Endpoint**: `GET /documents/{document_id}`

**Description**: Retrieve detailed information about a specific document.

**Functionality**:

- Gets document by ID
- Includes all metadata and processing status
- Shows chunk information

**Request**:

```http
GET /documents/1
```

**Response**: Same structure as document items in list response

---

## Session Management Endpoints

### 8. Get Session Information

**Endpoint**: `GET /chat/session/{session_id}`

**Description**: Get detailed information about a specific chat session.

**Functionality**:

- Retrieves session metadata
- Shows message count and creation time
- Includes user and company information
- Displays session name (auto-generated from first query)

**Request**:

```http
GET /chat/session/session-uuid
```

**Response**:

```json
{
  "sessionId": "session-uuid",
  "userId": "123e4567-e89b-12d3-a456-************",
  "companyId": "987fcdeb-51a2-43d1-b789-123456789abc",
  "createdAt": "2024-01-01T10:00:00Z",
  "messageCount": 10,
  "name": "Compliance Discussion"
}
```

---

### 9. List All Chat Sessions

**Endpoint**: `GET /chat/sessions`

**Description**: List all chat sessions with optional filtering.

**Functionality**:

- Lists all sessions (admin) or filtered sessions
- Includes session metadata and preview
- Shows activity information

**Request**:

```http
GET /chat/sessions?user_id=123e4567-e89b-12d3-a456-************&company_id=987fcdeb-51a2-43d1-b789-123456789abc
```

**Response**:

```json
{
  "sessions": [
    {
      "sessionId": "session-uuid",
      "userId": "123e4567-e89b-12d3-a456-************",
      "companyId": "987fcdeb-51a2-43d1-b789-123456789abc",
      "name": "Compliance Discussion",
      "createdAt": "2024-01-01T10:00:00Z",
      "lastActivity": "2024-01-01T11:00:00Z",
      "messageCount": 10,
      "preview": "What are the compliance requirements..."
    }
  ],
  "totalCount": 1
}
```

---

### 10. List Sessions by User

**Endpoint**: `GET /chat/sessions/user/{user_id}`

**Description**: List all chat sessions for a specific user.

**Functionality**:

- Filters sessions by user ID
- Includes all session metadata
- Shows user-specific activity

---

### 11. List Sessions by Company

**Endpoint**: `GET /chat/sessions/company/{company_id}`

**Description**: List all chat sessions for a specific company.

**Functionality**:

- Filters sessions by company ID
- Includes company-specific metadata
- Shows organizational activity

---

## Queue Management Endpoints

### 12. Get Task Status

**Endpoint**: `GET /upload/status/{task_id}`

**Description**: Get the processing status of a specific document upload task.

**Functionality**:

- Tracks document processing progress
- Shows queue position for queued tasks
- Includes error information for failed tasks
- Provides timing information

**Request**:

```http
GET /upload/status/task_1234567890_1
```

**Response**:

```json
{
  "taskId": "task_1234567890_1",
  "status": "processing",
  "progress": 70,
  "errorMessage": null,
  "chunksCreated": 35,
  "createdAt": 1704067200.0,
  "startedAt": 1704067201.0,
  "completedAt": null,
  "originalFilename": "compliance_policy.pdf",
  "queuePosition": null
}
```

**Response Fields**:

- `taskId`: Unique task identifier
- `status`: Current status (queued, processing, completed, failed)
- `progress`: Processing progress (0-100)
- `errorMessage`: Error details if failed
- `chunksCreated`: Number of chunks created
- `createdAt`: Task creation timestamp
- `startedAt`: Processing start timestamp
- `completedAt`: Completion timestamp
- `originalFilename`: Original filename
- `queuePosition`: Position in queue (if queued)

---

### 13. Get Queue Status

**Endpoint**: `GET /upload/queue/status`

**Description**: Get overall status of the document processing queue.

**Functionality**:

- Shows queue statistics
- Displays task counts by status
- Provides system health information

**Request**:

```http
GET /upload/queue/status
```

**Response**:

```json
{
  "queueSize": 5,
  "queuedTasks": 3,
  "processingTasks": 1,
  "completedTasks": 25,
  "failedTasks": 2,
  "totalTasks": 31
}
```

**Response Fields**:

- `queueSize`: Current queue size
- `queuedTasks`: Number of queued tasks
- `processingTasks`: Number of currently processing tasks
- `completedTasks`: Total completed tasks
- `failedTasks`: Total failed tasks
- `totalTasks`: Total tasks processed

---

## System Endpoints

### 14. Health Check

**Endpoint**: `GET /health`

**Description**: Simple health check endpoint to verify service status.

**Functionality**:

- Verifies service is running
- Returns basic status information
- Used for monitoring and load balancers

**Request**:

```http
GET /health
```

**Response**:

```json
{
  "status": "healthy",
  "service": "LE Compliance ChatBot"
}
```

---

## Error Handling

### Standard Error Response Format

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "detail": "Detailed error information"
}
```

### Common HTTP Status Codes

- `200 OK`: Successful operation
- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: Resource not found
- `413 Request Entity Too Large`: File size exceeds limit
- `500 Internal Server Error`: Server processing error

---

## Authentication & Authorization

**Current Status**: Basic UUID-based user identification

**Future Enhancements**:

- JWT token authentication
- Role-based access control
- API key management
- Rate limiting

---

## Rate Limiting

**Current Status**: No rate limiting implemented

**Future Enhancements**:

- Per-user rate limiting
- Per-endpoint rate limiting
- Burst protection

---

## File Upload Limits

- **Maximum File Size**: 30MB per file
- **Supported Formats**: PDF, DOCX, PPTX, XLSX, XLS, DOC, PPT
- **Multiple Upload Limit**: 10 files per request
- **Processing**: Sequential processing for multiple files

---

## Data Models

### Document Processing Pipeline

1. **Upload**: File validation and storage
2. **Extraction**: Text extraction with page/section metadata
3. **Chunking**: Intelligent text segmentation (500 chars with 50 char overlap)
4. **Embedding**: Vector generation using BAAI/bge-large-en-v1.5
5. **Storage**: PostgreSQL with pgvector for similarity search

### AI Response Generation

1. **Query Processing**: Convert user query to embedding
2. **Similarity Search**: Find relevant document chunks
3. **Context Preparation**: Format retrieved chunks
4. **AI Generation**: Generate response using Google Gemini
5. **Response Enhancement**: Optional agentic reasoning

### Enhanced Features

1. **Conversation Context Awareness**: The system now searches through previous conversation messages using embeddings to provide more contextual responses
2. **Multi-language Support**: Automatic language detection and response in the same language
3. **Document Citations**: Detailed tracking of which document chunks were used in responses
4. **Self-awareness**: The AI can explain its reasoning process and assess confidence levels
5. **Session Name Generation**: Automatic generation of meaningful session names from the first query

---

## Configuration

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/chatbot_db

# AI Services
GOOGLE_API_KEY=your_google_api_key
EMBEDDING_MODEL=BAAI/bge-large-en-v1.5

# File Processing
MAX_FILE_SIZE=31457280
VECTOR_DIMENSION=1024
CHUNK_SIZE=500
CHUNK_OVERLAP=50
FORCE_CPU=true
```

---

## Development & Testing

### Running the API

```bash
# Development
uvicorn main:app --reload --host 0.0.0.0 --port 8080

# Production
python main.py
```

### API Documentation

- **Interactive Docs**: `http://localhost:8080/docs` (Swagger UI)
- **ReDoc**: `http://localhost:8080/redoc` (Alternative docs)

### Testing Endpoints

```bash
# Health check
curl http://localhost:8080/health

# Upload document
curl -X POST "http://localhost:8080/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "user_id=123e4567-e89b-12d3-a456-************"

# Chat query
curl -X POST "http://localhost:8080/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the compliance requirements?",
    "userId": "123e4567-e89b-12d3-a456-************"
  }'

# Enhanced chat query
curl -X POST "http://localhost:8080/chat/enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the compliance requirements?",
    "userId": "123e4567-e89b-12d3-a456-************"
  }'
```

---

## Performance Considerations

### Optimization Features

- **Sequential Processing**: Multiple documents processed one at a time
- **Vector Search**: Efficient similarity search with pgvector
- **Connection Pooling**: Database connection optimization
- **Async Processing**: Non-blocking document processing
- **Context Window**: Limited conversation context for performance

### Monitoring

- **Queue Status**: Real-time queue monitoring
- **Task Progress**: Individual task progress tracking
- **Error Logging**: Comprehensive error logging
- **Performance Metrics**: Processing time tracking

---

## Security Considerations

### Current Security Features

- **File Validation**: Type and size validation
- **Input Sanitization**: Request parameter validation
- **Error Handling**: Secure error responses
- **CORS Configuration**: Configurable CORS settings

### Recommended Security Enhancements

- **Authentication**: JWT token implementation
- **Authorization**: Role-based access control
- **Rate Limiting**: API rate limiting
- **Input Validation**: Enhanced input sanitization
- **HTTPS**: SSL/TLS encryption
- **Audit Logging**: Security event logging

---

## New Features & Enhancements

### Enhanced Chat Capabilities

1. **Conversation Context Awareness**: The system now searches through previous conversation messages using embeddings to provide more contextual responses
2. **Multi-language Support**: Automatic language detection and response in the same language
3. **Document Citations**: Detailed tracking of which document chunks were used in responses
4. **Self-awareness**: The AI can explain its reasoning process and assess confidence levels
5. **Session Name Generation**: Automatic generation of meaningful session names from the first query

### Improved Data Models

1. **Enhanced Chunk Schema**: More detailed chunk information including document metadata
2. **Document Context**: Structured document citations with chunk details
3. **Conversation Context**: Previous message references with metadata
4. **Context Sources**: Tracking of information sources (documents vs conversation)

### AI Model Enhancements

1. **Google Gemini Integration**: Updated to use latest Gemini models with fallback options
2. **Agentic Reasoning**: Optional step-by-step reasoning mode
3. **Context-Aware Prompts**: Enhanced system prompts for better conversation flow
4. **Confidence Assessment**: Automatic confidence level determination

---

This documentation provides a comprehensive overview of all backend endpoints, their functionality, request/response formats, and implementation details for the LE Compliance ChatBot API, including all recent enhancements and improvements.
