from fastapi import (
    FastAPI,
    File,
    UploadFile,
    Depends,
    HTTPException,
    status,
    Form,
    Query,
)
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from uuid import UUID
from contextlib import asynccontextmanager
from datetime import datetime

from database import get_db, init_db
from models import Base, ChatSession, ChatMessage
from document_service import DocumentService
from chat_service import ChatService
from queue_service import document_queue_service
from schemas import (
    DocumentUploadResponse,
    DocumentUploadQueuedResponse,
    ChatQueryRequest,
    ChatHistoryResponse,
    ChatMessageResponse,
    SessionInfoResponse,
    DocumentListResponse,
    ErrorResponse,
    SessionListResponse,
    SessionListItem,
    TaskStatusResponse,
    QueueStatusResponse,
    MultipleDocumentUploadResponse,
    DocumentUploadResult,
    EnhancedChatQueryResponse,
)
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize services
document_service = DocumentService()
chat_service = ChatService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for startup and shutdown"""
    # Startup
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    yield

    # Shutdown
    try:
        document_queue_service.shutdown()
        logger.info("Application shutdown complete")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI app
app = FastAPI(
    title="LE Compliance ChatBot",
    description="A chatbot for Legal and Ethical Compliance document analysis",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/upload", response_model=DocumentUploadQueuedResponse)
async def upload_document(
    file: UploadFile = File(...),
    userId: UUID = Form(..., alias="user_id"),
    companyId: Optional[UUID] = Form(None, alias="company_id"),
    documentType: Optional[str] = Form("Standard", alias="document_type"),
    db: Session = Depends(get_db),
):
    """
    Upload and queue a single document for processing

    Supported formats: PDF, DOCX, PPTX, XLSX, XLS
    """
    try:
        # Validate file size
        if file.size and file.size > config.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {config.MAX_FILE_SIZE} bytes",
            )

        # Validate file type
        file_extension = file.filename.split(
            ".")[-1].lower() if file.filename else ""
        if f".{file_extension}" not in config.SUPPORTED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported file type. Supported types: {list(config.SUPPORTED_EXTENSIONS.keys())}",
            )

        # Read file content
        file_content = await file.read()

        # Create document record first
        filename = file.filename or "unknown_file"
        result = document_service.create_document_record(
            file_content, filename, db, userId, companyId, documentType
        )

        # Add to processing queue
        task_id = document_queue_service.add_task(
            file_content, filename, userId, companyId, result["documentId"]
        )

        return DocumentUploadQueuedResponse(
            documentId=result["documentId"],
            taskId=task_id,
            filename=result["filename"],
            fileSize=result["fileSize"],
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process document: {str(e)}",
        )


@app.post("/upload/multiple", response_model=MultipleDocumentUploadResponse)
async def upload_multiple_documents(
    files: List[UploadFile] = File(...),
    userId: UUID = Form(..., alias="user_id"),
    companyId: Optional[UUID] = Form(None, alias="company_id"),
    documentType: Optional[str] = Form("Standard", alias="document_type"),
    db: Session = Depends(get_db),
):
    """
    Upload and queue multiple documents for processing

    Supported formats: PDF, DOCX, PPTX, XLSX, XLS
    Documents are processed one at a time to ensure efficient vector embedding generation.
    """
    try:
        if not files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No files provided"
            )

        if len(files) > 10:  # Limit to 10 files per request
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 10 files allowed per request",
            )

        upload_results = []

        for file in files:
            try:
                # Validate file size
                if file.size and file.size > config.MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        detail=f"File {file.filename} exceeds maximum allowed size of {config.MAX_FILE_SIZE} bytes",
                    )

                # Validate file type
                file_extension = (
                    file.filename.split(
                        ".")[-1].lower() if file.filename else ""
                )
                if f".{file_extension}" not in config.SUPPORTED_EXTENSIONS:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Unsupported file type for {file.filename}. Supported types: {list(config.SUPPORTED_EXTENSIONS.keys())}",
                    )

                # Read file content
                file_content = await file.read()

                # Create document record first
                filename = file.filename or "unknown_file"
                result = document_service.create_document_record(
                    file_content, filename, db, userId, companyId, documentType
                )

                # Get the full document record to populate all required fields
                document = document_service.get_document(
                    result["documentId"], db)

                # Add to processing queue (will be processed one at a time by the queue service)
                task_id = document_queue_service.add_task(
                    file_content, filename, userId, companyId, result["documentId"]
                )

                if document:
                    upload_results.append(
                        DocumentUploadResult(
                            documentId=result["documentId"],
                            taskId=task_id,
                            id=getattr(document, "id", 0),
                            filename=getattr(document, "filename", ""),
                            originalFilename=getattr(
                                document, "originalFilename", ""),
                            fileSize=getattr(document, "fileSize", 0),
                            fileType=getattr(document, "fileType", ""),
                            uploadedAt=getattr(
                                document, "uploadedAt", datetime.now()),
                            totalPages=getattr(document, "totalPages", None),
                            totalChunks=getattr(document, "totalChunks", None),
                            chunksCount=0,  # Will be updated during processing
                            documentType=getattr(
                                document, "documentType", "Standard"),
                        )
                    )

            except HTTPException:
                # Re-raise HTTP exceptions for individual files
                raise
            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {e}")
                # Continue with other files even if one fails
                upload_results.append(
                    DocumentUploadResult(
                        documentId=0,
                        taskId="",
                        id=0,
                        filename="",
                        originalFilename=file.filename or "unknown_file",
                        fileSize=0,
                        fileType="",
                        uploadedAt=datetime.now(),
                        totalPages=None,
                        totalChunks=None,
                        chunksCount=0,
                        documentType="Standard",
                        status="failed",
                        message=f"Failed to process file: {str(e)}",
                    )
                )

        return MultipleDocumentUploadResponse(
            uploads=upload_results,
            totalCount=len(upload_results),
            message=f"Successfully queued {len([r for r in upload_results if r.status == 'queued'])} documents for processing",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading multiple documents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process documents: {str(e)}",
        )



@app.post("/chat", response_model=EnhancedChatQueryResponse)
async def enhanced_chat_query(request: ChatQueryRequest, db: Session = Depends(get_db)):
    """
    Send a chat query and get AI response with conversation context awareness
    """
    print(
        f"Received enhanced chat query: {request.query} for user {request.userId} in session {request.sessionId}, company {request.companyId}"
    )
    try:
        result = chat_service.process_query(
            request.query,
            request.sessionId,  # Do not force a shared default; let the service create a new session if needed
            db,
            request.userId,
            request.companyId,
        )

        # Get the latest assistant message from the database
        session = db.query(ChatSession).filter(ChatSession.sessionId == result["sessionId"]).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        latest_message = (
            db.query(ChatMessage)
            .filter(ChatMessage.sessionId == session.id, ChatMessage.messageType == "assistant")
            .order_by(ChatMessage.id.desc())
            .first()
        )
        
        if not latest_message:
            raise HTTPException(status_code=404, detail="Assistant message not found")

        # Parse document context from JSON if it exists
        document_context = None
        if latest_message.documentContext:
            try:
                document_context = chat_service._parse_document_context_from_db(latest_message.documentContext)
            except Exception as e:
                logger.warning(f"Failed to parse document context: {e}")
                document_context = None

        chat_response = EnhancedChatQueryResponse(
            id=latest_message.id,
            sessionId=result["sessionId"],
            messageType=latest_message.messageType,
            content=latest_message.content,
            timestamp=latest_message.timestamp.isoformat(),
            queryType=latest_message.queryType,
            confidence=latest_message.confidence,
            reasoning=latest_message.reasoning,
            documentContext=document_context,
        )

        print(chat_response.model_dump_json())
        return chat_response

    except Exception as e:
        logger.error(f"Error processing enhanced chat query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process enhanced query: {str(e)}",
        )


@app.get("/chat/history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """
    Get chat history for a session
    """
    try:
        messages = chat_service.get_chat_history(session_id, db)
        return ChatHistoryResponse(sessionId=session_id, messages=messages)

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat history: {str(e)}",
        )


@app.get("/chat/session/{session_id}", response_model=SessionInfoResponse)
async def get_session_info(session_id: str, db: Session = Depends(get_db)):
    """
    Get session information
    """
    try:
        session_info = chat_service.get_session_info(session_id, db)
        if not session_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
            )
        return SessionInfoResponse(**session_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session info: {str(e)}",
        )


@app.get("/documents", response_model=DocumentListResponse)
async def list_documents(
    userId: Optional[UUID] = Query(
        None, alias="user_id", description="Filter by user ID"
    ),
    companyId: Optional[UUID] = Query(
        None, alias="company_id", description="Filter by company ID"
    ),
    db: Session = Depends(get_db),
):
    """
    List documents with optional filtering by user_id and company_id
    """
    try:
        if userId:
            # Get documents filtered by user and company
            documents_info = document_service.get_document_ids_by_user_and_company(
                userId, companyId, db
            )
            documents = []
            for doc_info in documents_info:
                doc = document_service.get_document(doc_info["id"], db)
                if doc:
                    documents.append(doc)
        else:
            # List all documents (admin functionality)
            documents = document_service.list_documents(db)

        document_list = []
        for doc in documents:
            doc_id = getattr(doc, "id", 0)
            chunks_count = len(
                document_service.get_document_chunks(doc_id, db))
            document_list.append(
                {
                    "id": doc_id,
                    "filename": getattr(doc, "filename", ""),
                    "originalFilename": getattr(doc, "originalFilename", ""),
                    "fileSize": getattr(doc, "fileSize", 0),
                    "fileType": getattr(doc, "fileType", ""),
                    "uploadedAt": getattr(doc, "uploadedAt", datetime.now()),
                    "totalPages": getattr(doc, "totalPages", None),
                    "totalChunks": getattr(doc, "totalChunks", None),
                    "chunksCount": chunks_count,
                    "documentType": getattr(doc, "documentType", "Standard"),
                    "isVectorEmbeddingComplete": getattr(
                        doc, "isVectorEmbeddingComplete", False
                    ),
                }
            )

        return DocumentListResponse(
            documents=document_list, totalCount=len(document_list)
        )

    except Exception as e:
        logger.error(f"Error listing documents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list documents: {str(e)}",
        )


@app.get("/documents/{document_id}", response_model=DocumentUploadResponse)
async def get_document(document_id: int, db: Session = Depends(get_db)):
    """
    Get a single document by ID
    """
    try:
        document = document_service.get_document(document_id, db)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        doc_id = getattr(document, "id", 0)
        chunks_count = len(document_service.get_document_chunks(doc_id, db))

        return DocumentUploadResponse(
            id=doc_id,
            filename=getattr(document, "filename", ""),
            originalFilename=getattr(document, "originalFilename", ""),
            fileSize=getattr(document, "fileSize", 0),
            fileType=getattr(document, "fileType", ""),
            uploadedAt=getattr(document, "uploadedAt", datetime.now()),
            totalPages=getattr(document, "totalPages", None),
            totalChunks=getattr(document, "totalChunks", None),
            chunksCount=chunks_count,
            documentType=getattr(document, "documentType", "Standard"),
            isVectorEmbeddingComplete=getattr(
                document, "isVectorEmbeddingComplete", False
            ),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get document: {str(e)}",
        )


@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {"status": "healthy", "service": "LE Compliance ChatBot"}


@app.get("/upload/status/{task_id}", response_model=TaskStatusResponse)
async def get_upload_status(task_id: str):
    """
    Get the status of a document processing task
    """
    try:
        task_status = document_queue_service.get_task_status(task_id)
        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Task not found"
            )

        # Add queue position information
        if task_status.get("status") == "queued":
            queue_position = document_queue_service.get_queue_position(task_id)
            task_status["queuePosition"] = queue_position

        return TaskStatusResponse(**task_status)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}",
        )


@app.get("/upload/queue/status", response_model=QueueStatusResponse)
async def get_queue_status():
    """
    Get the overall status of the document processing queue
    """
    try:
        queue_status = document_queue_service.get_queue_status()
        return QueueStatusResponse(**queue_status)

    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get queue status: {str(e)}",
        )


@app.get("/chat/sessions", response_model=SessionListResponse)
async def list_chat_sessions(
    userId: Optional[UUID] = Query(
        None, alias="user_id", description="Filter by user ID"
    ),
    companyId: Optional[UUID] = Query(
        None, alias="company_id", description="Filter by company ID"
    ),
    db: Session = Depends(get_db),
):
    """
    List chat sessions with optional filtering by user_id and/or company_id
    """
    try:
        sessions = chat_service.list_all_sessions(
            db, user_id=userId, company_id=companyId
        )

        # Convert dictionary sessions to SessionListItem objects
        session_list = [SessionListItem(**session) for session in sessions]
        return SessionListResponse(sessions=session_list, totalCount=len(session_list))

    except Exception as e:
        logger.error(f"Error listing chat sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list chat sessions: {str(e)}",
        )


@app.get("/chat/sessions/user/{user_id}", response_model=SessionListResponse)
async def list_chat_sessions_by_user(user_id: UUID, db: Session = Depends(get_db)):
    """
    List chat sessions for a specific user
    """
    try:
        sessions = chat_service.list_sessions_by_user(db, user_id)

        # Convert dictionary sessions to SessionListItem objects
        session_list = [SessionListItem(**session) for session in sessions]
        return SessionListResponse(sessions=session_list, totalCount=len(session_list))

    except Exception as e:
        logger.error(f"Error listing chat sessions for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list chat sessions: {str(e)}",
        )


@app.get("/chat/sessions/company/{company_id}", response_model=SessionListResponse)
async def list_chat_sessions_by_company(
    company_id: UUID, db: Session = Depends(get_db)
):
    """
    List chat sessions for a specific company
    """
    try:
        sessions = chat_service.list_sessions_by_company(db, company_id)

        # Convert dictionary sessions to SessionListItem objects
        session_list = [SessionListItem(**session) for session in sessions]
        return SessionListResponse(sessions=session_list, totalCount=len(session_list))

    except Exception as e:
        logger.error(
            f"Error listing chat sessions for company {company_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list chat sessions: {str(e)}",
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
    # uvicorn.run(app, host="0.0.0.0", port=8080)
