import os
import shutil
import tempfile
import fitz  # PyMuPDF
from docx import Document as DocxDocument
from pptx import Presentation
import openpyxl
from typing import List, Dict, Any, Tuple
import logging
import re

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Handles text extraction from various document formats"""
    
    def __init__(self, upload_dir: str):
        self.upload_dir = upload_dir
        os.makedirs(upload_dir, exist_ok=True)
    
    def sanitize_text(self, text: str) -> str:
        """Remove NUL characters and other problematic characters from text"""
        if not text:
            return ""
        
        # Remove NUL characters (0x00) - these cause PostgreSQL errors
        text = text.replace('\x00', '')
        
        # Remove other control characters that might cause issues
        # Keep newlines, tabs, and carriage returns but remove others
        text = ''.join(char for char in text if char >= ' ' or char in '\n\t\r')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def extract_text_from_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract text from PDF file with page information"""
        try:
            doc = fitz.open(file_path)
            pages_data = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                
                # Sanitize the extracted text
                text = self.sanitize_text(text)
                
                # Extract headers and footers (common patterns)
                lines = text.split('\n')
                header = lines[0] if lines else ""
                footer = lines[-1] if lines else ""
                
                # Try to identify section headers (common patterns)
                section = self._identify_section(text)
                
                pages_data.append({
                    'page_number': page_num + 1,
                    'text': text,
                    'section': section,
                    'header': header,
                    'footer': footer
                })
            
            doc.close()
            return pages_data
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            raise
    
    def extract_text_from_docx(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract text from DOCX file with section information"""
        try:
            doc = DocxDocument(file_path)
            pages_data = []
            current_page = 1
            current_section = "Introduction"
            
            # Group paragraphs by potential page breaks
            paragraphs = []
            for para in doc.paragraphs:
                text = para.text.strip()
                if text:
                    # Sanitize the paragraph text
                    text = self.sanitize_text(text)
                    
                    # Check if this might be a section header
                    if self._is_section_header(text):
                        current_section = text
                    
                    paragraphs.append({
                        'text': text,
                        'section': current_section
                    })
            
            # Estimate page breaks (rough approximation)
            chars_per_page = 2000  # Rough estimate
            current_text = ""
            page_paragraphs = []
            
            for para in paragraphs:
                if len(current_text) + len(para['text']) > chars_per_page and current_text:
                    pages_data.append({
                        'page_number': current_page,
                        'text': current_text,
                        'section': para['section'],
                        'header': "",
                        'footer': ""
                    })
                    current_page += 1
                    current_text = para['text']
                else:
                    current_text += "\n" + para['text'] if current_text else para['text']
            
            # Add the last page
            if current_text:
                pages_data.append({
                    'page_number': current_page,
                    'text': current_text,
                    'section': paragraphs[-1]['section'] if paragraphs else "Unknown",
                    'header': "",
                    'footer': ""
                })
            
            return pages_data
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {e}")
            raise
    
    def extract_text_from_pptx(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract text from PPTX file with slide information"""
        try:
            prs = Presentation(file_path)
            pages_data = []
            
            for slide_num, slide in enumerate(prs.slides):
                text = ""
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
                
                # Sanitize the extracted text
                text = self.sanitize_text(text)
                
                # Try to identify section from slide title or content
                section = self._identify_section(text)
                
                pages_data.append({
                    'page_number': slide_num + 1,
                    'text': text,
                    'section': section,
                    'header': "",
                    'footer': ""
                })
            
            return pages_data
        except Exception as e:
            logger.error(f"Error extracting text from PPTX {file_path}: {e}")
            raise
    
    def extract_text_from_excel(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract text from Excel file with sheet information"""
        try:
            wb = openpyxl.load_workbook(file_path, data_only=True)
            pages_data = []
            
            for sheet_num, sheet_name in enumerate(wb.sheetnames):
                sheet = wb[sheet_name]
                text = f"Sheet: {sheet_name}\n"
                
                for row in sheet.iter_rows(values_only=True):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    if row_text.strip():
                        text += row_text + "\n"
                
                # Sanitize the extracted text
                text = self.sanitize_text(text)
                
                pages_data.append({
                    'page_number': sheet_num + 1,
                    'text': text,
                    'section': sheet_name,
                    'header': f"Sheet: {sheet_name}",
                    'footer': ""
                })
            
            return pages_data
        except Exception as e:
            logger.error(f"Error extracting text from Excel {file_path}: {e}")
            raise
    
    def extract_text(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """Extract text based on file type with page and section information"""
        file_extension = file_type.lower()
        
        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_extension == '.docx':
            return self.extract_text_from_docx(file_path)
        elif file_extension == '.pptx':
            return self.extract_text_from_pptx(file_path)
        elif file_extension in ['.xlsx', '.xls']:
            return self.extract_text_from_excel(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    
    def _identify_section(self, text: str) -> str:
        """Identify section from text content"""
        lines = text.split('\n')
        
        # Look for common section patterns
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line:
                # Common section header patterns
                if re.match(r'^[0-9]+\.', line):  # Numbered sections
                    return line
                elif re.match(r'^[A-Z][A-Z\s]+$', line) and len(line) < 100:  # ALL CAPS headers
                    return line
                elif re.match(r'^[A-Z][a-z\s]+:', line):  # Title case with colon
                    return line
                elif line.isupper() and len(line) < 50:  # Short uppercase text
                    return line
                elif re.match(r'^Chapter\s+\d+', line, re.IGNORECASE):
                    return line
                elif re.match(r'^Section\s+\d+', line, re.IGNORECASE):
                    return line
        
        return "Main Content"
    
    def _is_section_header(self, text: str) -> bool:
        """Check if text looks like a section header"""
        if not text or len(text) > 200:
            return False
        
        # Common header patterns
        patterns = [
            r'^[0-9]+\.',  # Numbered sections
            r'^[A-Z][A-Z\s]+$',  # ALL CAPS headers
            r'^[A-Z][a-z\s]+:',  # Title case with colon
            r'^Chapter\s+\d+',  # Chapter headers
            r'^Section\s+\d+',  # Section headers
            r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$'  # Title case words
        ]
        
        for pattern in patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def save_file(self, file_content: bytes, filename: str) -> str:
        """Save uploaded file to disk safely and atomically."""
    
        if not isinstance(file_content, bytes):
            raise TypeError("file_content must be bytes")
    
        os.makedirs(self.upload_dir, exist_ok=True)
        filename = os.path.basename(filename)  # Prevent path traversal
        final_path = os.path.join(self.upload_dir, filename)

        try:
        # Create a temp file in the same directory to ensure atomic move
            with tempfile.NamedTemporaryFile(dir=self.upload_dir, delete=False) as tmp_file:
                tmp_file.write(file_content)
                temp_path = tmp_file.name

        # Replace the old file atomically
            shutil.move(temp_path, final_path)

        except Exception as e:
        # Clean up temp file if something goes wrong
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.remove(temp_path)
            raise RuntimeError(f"Error saving file {filename}: {e}")

        return os.path.abspath(final_path)