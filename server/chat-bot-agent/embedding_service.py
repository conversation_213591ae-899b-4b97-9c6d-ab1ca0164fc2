import numpy as np
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional
import logging
from config import config

logger = logging.getLogger(__name__)

class EmbeddingService:
    """Handles text embedding generation using BAAI/bge-large-en-v1.5 model"""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the embedding service with configurable model
        
        Args:
            model_name: Name of the sentence transformer model to use (defaults to config)
        """
        try:
            # Use configurable model name
            model_name = model_name or config.EMBEDDING_MODEL
            
            # Handle device selection based on config
            import torch
            if config.FORCE_CPU or (torch.backends.mps.is_available() and config.FORCE_CPU):
                logger.info("Forcing CPU usage to avoid MPS memory issues")
                device = 'cpu'
            elif torch.backends.mps.is_available():
                logger.info("Using MPS (Metal Performance Shaders) for acceleration")
                device = 'mps'
            else:
                logger.info("Using CPU (MPS not available)")
                device = 'cpu'
            
            self.model = SentenceTransformer(model_name, device=device)
            self.dimension = self.model.get_sentence_embedding_dimension()
            
            # Validate that the model dimension matches our configuration
            if self.dimension != config.VECTOR_DIMENSION:
                logger.warning(f"Model dimension ({self.dimension}) doesn't match config ({config.VECTOR_DIMENSION})")
                logger.warning(f"Please update VECTOR_DIMENSION in your .env file to: {self.dimension}")
            
            logger.info(f"Loaded {model_name} embedding model with dimension: {self.dimension} on {device}")
        except Exception as e:
            logger.error(f"Error loading {model_name} embedding model: {e}")
            raise
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text using BAAI/bge-large-en-v1.5 model
        
        Args:
            text: Input text to embed
            
        Returns:
            List of floats representing the embedding vector
        """
        try:
            embedding = self.model.encode(text, convert_to_tensor=False)
            # Convert numpy array to list
            if isinstance(embedding, np.ndarray):
                return embedding.tolist()
            else:
                # Ensure we convert any tensor to list of floats
                return [float(x) for x in embedding]
        except Exception as e:
            logger.error(f"Error generating embedding for text: {e}")
            raise
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batch using BAAI/bge-large-en-v1.5 model
        
        Args:
            texts: List of input texts to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            embeddings = self.model.encode(texts, convert_to_tensor=False)
            # Convert numpy array to list of lists
            if isinstance(embeddings, np.ndarray):
                return embeddings.tolist()
            else:
                # Ensure we convert any tensors to lists of floats
                return [[float(x) for x in emb] for emb in embeddings]
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {e}")
            raise
    
    def generate_chunk_embeddings(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for document chunks
        
        Args:
            chunks: List of chunk dictionaries with 'chunk_text' key
            
        Returns:
            List of chunks with added 'embedding' key
        """
        try:
            texts = [chunk['chunk_text'] for chunk in chunks]
            embeddings = self.generate_embeddings_batch(texts)
            
            # Add embeddings to chunks
            for chunk, embedding in zip(chunks, embeddings):
                chunk['embedding'] = embedding
            
            return chunks
        except Exception as e:
            logger.error(f"Error generating chunk embeddings: {e}")
            raise
    
    def similarity_search(self, query_embedding: List[float], chunk_embeddings: List[List[float]], top_k: Optional[int] = 10) -> List[int]:
        """
        Perform similarity search between query and chunk embeddings
        
        Args:
            query_embedding: Query embedding vector
            chunk_embeddings: List of chunk embedding vectors
            top_k: Number of top similar chunks to return
            
        Returns:
            List of indices of top-k most similar chunks
        """
        try:
            top_k = top_k or config.SIMILARITY_TOP_K
            
            # Convert to numpy arrays for efficient computation
            query_vec = np.array(query_embedding)
            chunk_vecs = np.array(chunk_embeddings)
            
            # Calculate cosine similarities
            similarities = np.dot(chunk_vecs, query_vec) / (
                np.linalg.norm(chunk_vecs, axis=1) * np.linalg.norm(query_vec)
            )
            
            # Get top-k indices
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            return top_indices.tolist()
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            raise 